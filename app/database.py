"""
Helper script to initialize data for the Phyllo dummy API.
This module provides functions to create initial database entries.
"""

from datetime import datetime
from typing import Dict, List, Any
import json
import uuid
from .database_connection import db_manager, get_platforms

def get_db():
    """Get database instance (memory or postgres session)"""
    return db_manager.get_memory_db()

def create_initial_data():
    """Create initial data for testing purposes"""
    memory_db = db_manager.get_memory_db()

    # Try to load profile analytics data first
    try:
        with open("generated_profile_analytics.json", "r") as f:
            profile_analytics = json.load(f)
    except FileNotFoundError:
        try:
            from generate_profile_analytics import generate_profiles
            profile_analytics = generate_profiles(100)  # Generate 100 profiles for initial data
            with open("generated_profile_analytics.json", "w") as f:
                json.dump(profile_analytics, f, indent=2)
        except ImportError:
            profile_analytics = []
            print("Warning: Could not generate profile analytics data")

    # Create sample users based on profile analytics
    users = []
    for i, profile in enumerate(profile_analytics[:3]):  # Use first 3 profiles for users
        users.append({
            "id": f"user_{uuid.uuid4().hex[:8]}",
            "name": profile["profile"]["full_name"],
            "email": f"{profile['profile']['platform_username']}@example.com",
            "status": "active",
            "platform": profile["work_platform"]["name"]
        })

    # Add users to database
    for user in users:
        memory_db["users"][user["id"]] = user
    
    # Create sample accounts
    accounts = [
        {
            "id": "account_12345",
            "user_id": "user_12345678",
            "platform_id": "platform_1",
            "status": "connected",
            "connected_at": datetime.now().isoformat()
        },
        {
            "id": "account_23456",
            "user_id": "user_12345678",
            "platform_id": "platform_3",
            "status": "connected",
            "connected_at": datetime.now().isoformat()
        },
        {
            "id": "account_34567",
            "user_id": "user_87654321",
            "platform_id": "platform_2",
            "status": "connected", 
            "connected_at": datetime.now().isoformat()
        },
        {
            "id": "account_45678",
            "user_id": "user_11223344",
            "platform_id": "platform_5",
            "status": "connected",
            "connected_at": datetime.now().isoformat()
        }
    ]
    
    # Add accounts to database
    for account in accounts:
        memory_db["accounts"][account["id"]] = account

    # Load profile analytics data
    try:
        with open("generated_profile_analytics.json", "r") as f:
            profile_analytics = json.load(f)
    except FileNotFoundError:
        # If file doesn't exist, generate it
        from generate_profile_analytics import generate_profiles
        profile_analytics = generate_profiles(100)  # Generate 100 profiles for initial data
        with open("generated_profile_analytics.json", "w") as f:
            json.dump(profile_analytics, f, indent=2)

    # Create data entries from profile analytics
    data_entries = []
    for profile in profile_analytics[:5]:  # Use first 5 profiles for initial data
        data_entries.append({
            "id": f"data_{uuid.uuid4().hex[:8]}",
            "account_id": f"account_{uuid.uuid4().hex[:8]}",
            "data_type": "profile_analytics",
            "content": profile,
            "created_at": datetime.now().isoformat()
        })

    # Add data to database
    for data in data_entries:
        memory_db["data"][data["id"]] = data

    print(f"Initialized database with {len(memory_db['users'])} users, "
          f"{len(memory_db['accounts'])} accounts, and {len(memory_db['data'])} data entries")
