"""
Phyllo-compatible API endpoints.
These endpoints mimic the structure and responses of Phyllo's actual API.
"""

from fastapi import APIRouter, HTTPException, Query, Path, Depends, Body
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator, model_validator
from datetime import datetime
import json
import uuid
import re
from .database_connection import get_db, get_memory_db
from .phyllo_models import (
    PhylloResponse, PaginatedResponse, ProfileSearchRequest,
    ProfileAnalytics, PhylloProfile, PhylloWorkPlatform
)

# Create router for Phyllo-compatible endpoints
phyllo_router = APIRouter(prefix="/v1/social/creator")



# Supported platforms and their constraints
SUPPORTED_PLATFORMS = {
    "instagram": {
        "name": "Instagram",
        "max_follower_count": 500_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["REEL", "STORY", "STATIC_POST", "CAROUSEL"],
        "username_pattern": r"^[a-zA-Z0-9._]{1,30}$"
    },
    "youtube": {
        "name": "YouTube",
        "max_follower_count": 200_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["VIDEO"],
        "username_pattern": r"^[a-zA-Z0-9._-]{1,100}$"
    },
    "tiktok": {
        "name": "TikTok",
        "max_follower_count": 150_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["VIDEO", "REEL"],
        "username_pattern": r"^[a-zA-Z0-9._]{1,24}$"
    },
    "twitter": {
        "name": "Twitter",
        "max_follower_count": 130_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["STATIC_POST"],
        "username_pattern": r"^[a-zA-Z0-9_]{1,15}$"
    },
    "twitch": {
        "name": "Twitch",
        "max_follower_count": 20_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["VIDEO"],
        "username_pattern": r"^[a-zA-Z0-9_]{4,25}$"
    }
}

VALID_GENDERS = ["MALE", "FEMALE"]
VALID_AGE_GROUPS = ["13-17", "18-24", "25-34", "35-44", "45-54", "55-64", "65+"]
VALID_CONTENT_TYPES = ["VIDEO", "IMAGE", "REEL", "STORY", "STATIC_POST", "CAROUSEL"]

# Request/Response models for the specific endpoints
class ProfileAnalyticsRequest(BaseModel):
    profile_id: str = Field(..., description="The profile ID to get analytics for", min_length=1, max_length=100)
    include_audience: bool = Field(True, description="Include audience demographics")
    include_content: bool = Field(True, description="Include top content")
    include_pricing: bool = Field(True, description="Include pricing information")

    @field_validator('profile_id')
    @classmethod
    def validate_profile_id(cls, v):
        if not v or not v.strip():
            raise ValueError('Profile ID cannot be empty')
        # Basic UUID format validation (flexible to support different ID formats)
        if len(v.strip()) < 8:
            raise ValueError('Profile ID must be at least 8 characters long')
        return v.strip()

class QuickSearchRequest(BaseModel):
    platform: Optional[str] = Field(None, description="Platform to search (instagram, youtube, tiktok, twitter, twitch)")
    username: Optional[str] = Field(None, description="Username to search for", min_length=1, max_length=100)
    follower_count_min: Optional[int] = Field(None, description="Minimum follower count", ge=0)
    follower_count_max: Optional[int] = Field(None, description="Maximum follower count", ge=0)
    engagement_rate_min: Optional[float] = Field(None, description="Minimum engagement rate", ge=0.0, le=1.0)
    engagement_rate_max: Optional[float] = Field(None, description="Maximum engagement rate", ge=0.0, le=1.0)
    verified_only: Optional[bool] = Field(False, description="Only verified accounts")
    limit: int = Field(10, description="Number of results to return", ge=1, le=100)

    @field_validator('platform')
    @classmethod
    def validate_platform(cls, v):
        if v is not None:
            v = v.lower().strip()
            if v not in SUPPORTED_PLATFORMS:
                raise ValueError(f'Platform must be one of: {", ".join(SUPPORTED_PLATFORMS.keys())}')
        return v

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if v is not None:
            v = v.strip()
            if not v:
                raise ValueError('Username cannot be empty if provided')
            # Basic sanitization - remove potentially harmful characters
            if re.search(r'[<>"\'\\\x00-\x1f\x7f-\x9f]', v):
                raise ValueError('Username contains invalid characters')
        return v

    @model_validator(mode='after')
    def validate_follower_counts(self):
        min_count = self.follower_count_min
        max_count = self.follower_count_max
        platform = self.platform

        if min_count is not None and max_count is not None:
            if min_count > max_count:
                raise ValueError('follower_count_min cannot be greater than follower_count_max')

        # Platform-specific validation
        if platform and platform in SUPPORTED_PLATFORMS:
            max_allowed = SUPPORTED_PLATFORMS[platform]['max_follower_count']
            if max_count and max_count > max_allowed:
                raise ValueError(f'follower_count_max exceeds platform limit for {platform}: {max_allowed:,}')
            if min_count and min_count > max_allowed:
                raise ValueError(f'follower_count_min exceeds platform limit for {platform}: {max_allowed:,}')

        return self

    @model_validator(mode='after')
    def validate_engagement_rates(self):
        min_rate = self.engagement_rate_min
        max_rate = self.engagement_rate_max

        if min_rate is not None and max_rate is not None:
            if min_rate > max_rate:
                raise ValueError('engagement_rate_min cannot be greater than engagement_rate_max')

        return self

class AdvancedSearchRequest(BaseModel):
    platform: Optional[str] = Field(None, description="Platform to search")
    username: Optional[str] = Field(None, description="Username to search for", min_length=1, max_length=100)
    follower_count_min: Optional[int] = Field(None, description="Minimum follower count", ge=0)
    follower_count_max: Optional[int] = Field(None, description="Maximum follower count", ge=0)
    engagement_rate_min: Optional[float] = Field(None, description="Minimum engagement rate", ge=0.0, le=1.0)
    engagement_rate_max: Optional[float] = Field(None, description="Maximum engagement rate", ge=0.0, le=1.0)
    interests: Optional[List[str]] = Field(default=[], description="List of interests to filter by")
    location: Optional[str] = Field(None, description="Location to filter by", min_length=1, max_length=100)
    gender: Optional[str] = Field(None, description="Gender to filter by (MALE/FEMALE)")
    age_group: Optional[str] = Field(None, description="Age group to filter by")
    verified_only: Optional[bool] = Field(False, description="Only verified accounts")
    content_type: Optional[str] = Field(None, description="Content type preference")
    limit: int = Field(10, description="Number of results to return", ge=1, le=100)
    offset: int = Field(0, description="Offset for pagination", ge=0)

    @field_validator('platform')
    @classmethod
    def validate_platform(cls, v):
        if v is not None:
            v = v.lower().strip()
            if v not in SUPPORTED_PLATFORMS:
                raise ValueError(f'Platform must be one of: {", ".join(SUPPORTED_PLATFORMS.keys())}')
        return v

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if v is not None:
            v = v.strip()
            if not v:
                raise ValueError('Username cannot be empty if provided')
            # Basic sanitization
            if re.search(r'[<>"\'\\\x00-\x1f\x7f-\x9f]', v):
                raise ValueError('Username contains invalid characters')
        return v

    @field_validator('interests')
    @classmethod
    def validate_interests(cls, v):
        if v:
            if len(v) > 20:
                raise ValueError('Maximum 20 interests allowed')
            cleaned_interests = []
            for interest in v:
                if isinstance(interest, str):
                    interest = interest.strip()
                    if interest:
                        # Basic sanitization
                        if re.search(r'[<>"\'\\\x00-\x1f\x7f-\x9f]', interest):
                            raise ValueError(f'Interest "{interest}" contains invalid characters')
                        if len(interest) > 50:
                            raise ValueError(f'Interest "{interest}" is too long (max 50 characters)')
                        cleaned_interests.append(interest)
            return cleaned_interests
        return v

    @field_validator('location')
    @classmethod
    def validate_location(cls, v):
        if v is not None:
            v = v.strip()
            if not v:
                raise ValueError('Location cannot be empty if provided')
            # Basic sanitization
            if re.search(r'[<>"\'\\\x00-\x1f\x7f-\x9f]', v):
                raise ValueError('Location contains invalid characters')
        return v

    @field_validator('gender')
    @classmethod
    def validate_gender(cls, v):
        if v is not None:
            v = v.upper().strip()
            if v not in VALID_GENDERS:
                raise ValueError(f'Gender must be one of: {", ".join(VALID_GENDERS)}')
        return v

    @field_validator('age_group')
    @classmethod
    def validate_age_group(cls, v):
        if v is not None:
            v = v.strip()
            if v not in VALID_AGE_GROUPS:
                raise ValueError(f'Age group must be one of: {", ".join(VALID_AGE_GROUPS)}')
        return v

    @field_validator('content_type')
    @classmethod
    def validate_content_type(cls, v):
        if v is not None:
            v = v.upper().strip()
            if v not in VALID_CONTENT_TYPES:
                raise ValueError(f'Content type must be one of: {", ".join(VALID_CONTENT_TYPES)}')
        return v

    @model_validator(mode='after')
    def validate_follower_counts(self):
        min_count = self.follower_count_min
        max_count = self.follower_count_max
        platform = self.platform

        if min_count is not None and max_count is not None:
            if min_count > max_count:
                raise ValueError('follower_count_min cannot be greater than follower_count_max')

        # Platform-specific validation
        if platform and platform in SUPPORTED_PLATFORMS:
            max_allowed = SUPPORTED_PLATFORMS[platform]['max_follower_count']
            if max_count and max_count > max_allowed:
                raise ValueError(f'follower_count_max exceeds platform limit for {platform}: {max_allowed:,}')
            if min_count and min_count > max_allowed:
                raise ValueError(f'follower_count_min exceeds platform limit for {platform}: {max_allowed:,}')

        return self

    @model_validator(mode='after')
    def validate_engagement_rates(self):
        min_rate = self.engagement_rate_min
        max_rate = self.engagement_rate_max

        if min_rate is not None and max_rate is not None:
            if min_rate > max_rate:
                raise ValueError('engagement_rate_min cannot be greater than engagement_rate_max')

        return self

    @model_validator(mode='after')
    def validate_platform_content_type(self):
        platform = self.platform
        content_type = self.content_type

        if platform and content_type and platform in SUPPORTED_PLATFORMS:
            supported_types = SUPPORTED_PLATFORMS[platform]['supported_content_types']
            if content_type not in supported_types:
                raise ValueError(f'Content type "{content_type}" not supported for platform "{platform}". Supported types: {", ".join(supported_types)}')

        return self

# Helper functions
def validate_platform_specific_constraints(platform: str, filters: Dict) -> List[str]:
    """Validate platform-specific constraints and return list of validation errors"""
    errors = []

    if platform not in SUPPORTED_PLATFORMS:
        return errors

    platform_config = SUPPORTED_PLATFORMS[platform]

    # Validate follower count limits
    max_followers = platform_config['max_follower_count']
    if filters.get('follower_count_min') and filters['follower_count_min'] > max_followers:
        errors.append(f'follower_count_min exceeds {platform} limit: {max_followers:,}')
    if filters.get('follower_count_max') and filters['follower_count_max'] > max_followers:
        errors.append(f'follower_count_max exceeds {platform} limit: {max_followers:,}')

    # Validate content type support
    if filters.get('content_type'):
        supported_types = platform_config['supported_content_types']
        if filters['content_type'] not in supported_types:
            errors.append(f'Content type "{filters["content_type"]}" not supported for {platform}. Supported: {", ".join(supported_types)}')

    # Validate username format if provided
    if filters.get('username'):
        username_pattern = platform_config['username_pattern']
        if not re.match(username_pattern, filters['username']):
            errors.append(f'Username format invalid for {platform}')

    return errors

def sanitize_search_input(value: str) -> str:
    """Sanitize search input to prevent injection attacks and XSS"""
    if not value:
        return value

    # Convert to string if not already
    value = str(value)

    # Remove potentially dangerous characters including SQL injection patterns
    # Remove script tags, SQL keywords, and control characters
    dangerous_patterns = [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',               # JavaScript protocol
        r'on\w+\s*=',                # Event handlers
        r'(union|select|insert|update|delete|drop|create|alter|exec|execute)\s+',  # SQL keywords
        r'[<>"\'\\\x00-\x1f\x7f-\x9f]',  # Control characters and quotes
        r'--',                        # SQL comments
        r'/\*.*?\*/',                # SQL block comments
    ]

    sanitized = value
    for pattern in dangerous_patterns:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)

    # Limit length to prevent buffer overflow attacks
    if len(sanitized) > 100:
        sanitized = sanitized[:100]

    # Remove leading/trailing whitespace
    return sanitized.strip()

def validate_rate_limits(request_count: int = 1) -> bool:
    """Basic rate limiting validation (placeholder for actual implementation)"""
    # In a real implementation, this would check against a rate limiting store
    # For now, just validate reasonable request patterns
    if request_count > 100:
        return False
    return True

def validate_content_safety(content: str) -> List[str]:
    """Validate content for safety and compliance"""
    errors = []

    if not content:
        return errors

    # Check for potentially harmful content
    harmful_patterns = [
        r'(password|secret|token|key)\s*[:=]\s*\w+',  # Credential patterns
        r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card patterns
        r'\b\d{3}-\d{2}-\d{4}\b',  # SSN patterns
        r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Email patterns (if not expected)
    ]

    for pattern in harmful_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            errors.append(f"Content contains potentially sensitive information")
            break

    # Check content length
    if len(content) > 10000:
        errors.append("Content exceeds maximum length limit")

    return errors

def validate_request_headers(headers: dict) -> List[str]:
    """Validate request headers for security"""
    errors = []

    # Check for suspicious user agents
    user_agent = headers.get('user-agent', '').lower()
    suspicious_agents = ['bot', 'crawler', 'spider', 'scraper']

    if any(agent in user_agent for agent in suspicious_agents):
        # Log but don't block - might be legitimate
        pass

    # Validate content type if present
    content_type = headers.get('content-type', '')
    if content_type and 'application/json' not in content_type.lower():
        errors.append("Invalid content type - expected application/json")

    return errors

def create_error_response(status_code: int, message: str, errors: Optional[List[str]] = None) -> HTTPException:
    """Create standardized error response"""
    detail = {
        "success": False,
        "message": message,
        "error_code": status_code
    }

    if errors:
        detail["errors"] = errors

    return HTTPException(status_code=status_code, detail=detail)

def validate_response_data(data: Any) -> Any:
    """Validate and sanitize response data before sending"""
    if isinstance(data, dict):
        sanitized = {}
        for key, value in data.items():
            # Sanitize string values
            if isinstance(value, str):
                sanitized[key] = sanitize_search_input(value)
            elif isinstance(value, (list, dict)):
                sanitized[key] = validate_response_data(value)
            else:
                sanitized[key] = value
        return sanitized
    elif isinstance(data, list):
        return [validate_response_data(item) for item in data]
    elif isinstance(data, str):
        return sanitize_search_input(data)
    else:
        return data

def log_security_event(event_type: str, details: str, request_info: Optional[dict] = None):
    """Log security events for monitoring (placeholder for actual logging)"""
    # In a real implementation, this would log to a security monitoring system
    import datetime
    timestamp = datetime.datetime.now().isoformat()

    log_entry = {
        "timestamp": timestamp,
        "event_type": event_type,
        "details": details,
        "request_info": request_info or {}
    }

    # For now, just print to console (in production, use proper logging)
    print(f"SECURITY_EVENT: {log_entry}")

def validate_search_request(filters: Dict) -> List[str]:
    """Validate search request parameters and return list of errors"""
    errors = []

    # Validate engagement rate ranges
    if filters.get('engagement_rate_min') is not None and filters.get('engagement_rate_max') is not None:
        if filters['engagement_rate_min'] > filters['engagement_rate_max']:
            errors.append('engagement_rate_min cannot be greater than engagement_rate_max')

    # Validate follower count ranges
    if filters.get('follower_count_min') is not None and filters.get('follower_count_max') is not None:
        if filters['follower_count_min'] > filters['follower_count_max']:
            errors.append('follower_count_min cannot be greater than follower_count_max')

    # Validate interests list
    if filters.get('interests'):
        if len(filters['interests']) > 20:
            errors.append('Maximum 20 interests allowed')
        for interest in filters['interests']:
            if len(interest) > 50:
                errors.append(f'Interest "{interest}" too long (max 50 characters)')

    # Platform-specific validation
    if filters.get('platform'):
        platform_errors = validate_platform_specific_constraints(filters['platform'], filters)
        errors.extend(platform_errors)

    return errors

def get_profile_analytics_data():
    """Get profile analytics data from memory database"""
    memory_db = get_memory_db()
    data_entries = list(memory_db.get("data", {}).values())

    # Filter for profile analytics data
    profile_analytics = []
    for entry in data_entries:
        if entry.get("data_type") == "profile_analytics":
            profile_analytics.append(entry["content"])

    return profile_analytics

def filter_profiles(profiles: List[Dict], filters: Dict) -> List[Dict]:
    """Apply filters to profile list with enhanced validation and security"""
    filtered = []

    # Sanitize string inputs
    sanitized_filters = {}
    for key, value in filters.items():
        if isinstance(value, str):
            sanitized_filters[key] = sanitize_search_input(value)
        elif isinstance(value, list) and key == "interests":
            sanitized_filters[key] = [sanitize_search_input(item) for item in value if isinstance(item, str)]
        else:
            sanitized_filters[key] = value

    for profile_data in profiles:
        profile = profile_data.get("profile", {})
        work_platform = profile_data.get("work_platform", {})

        # Platform filter with exact matching
        if sanitized_filters.get("platform"):
            platform_name = work_platform.get("name", "").lower()
            filter_platform = sanitized_filters["platform"].lower()

            # Exact platform matching
            if filter_platform not in platform_name and platform_name not in filter_platform:
                continue

            # Validate platform-specific constraints
            platform_errors = validate_platform_specific_constraints(filter_platform, sanitized_filters)
            if platform_errors:
                # Skip profiles that don't meet platform constraints
                continue

        # Username filter with improved matching
        if sanitized_filters.get("username"):
            username = profile.get("platform_username", "").lower()
            filter_username = sanitized_filters["username"].lower()
            if filter_username not in username:
                continue

        # Follower count filters with validation
        follower_count = profile.get("follower_count", 0)
        if sanitized_filters.get("follower_count_min") is not None:
            if follower_count < sanitized_filters["follower_count_min"]:
                continue
        if sanitized_filters.get("follower_count_max") is not None:
            if follower_count > sanitized_filters["follower_count_max"]:
                continue

        # Engagement rate filters with validation
        engagement_rate = profile.get("engagement_rate", 0.0)
        if sanitized_filters.get("engagement_rate_min") is not None:
            if engagement_rate < sanitized_filters["engagement_rate_min"]:
                continue
        if sanitized_filters.get("engagement_rate_max") is not None:
            if engagement_rate > sanitized_filters["engagement_rate_max"]:
                continue

        # Verified filter
        if sanitized_filters.get("verified_only") and not profile.get("is_verified", False):
            continue

        # Gender filter with case-insensitive matching
        if sanitized_filters.get("gender"):
            profile_gender = profile.get("gender", "").upper()
            filter_gender = sanitized_filters["gender"].upper()
            if profile_gender != filter_gender:
                continue

        # Age group filter
        if sanitized_filters.get("age_group"):
            if profile.get("age_group") != sanitized_filters["age_group"]:
                continue

        # Content type filter (check if profile supports the content type)
        if sanitized_filters.get("content_type"):
            platform_name = work_platform.get("name", "").lower()
            if platform_name in SUPPORTED_PLATFORMS:
                supported_types = SUPPORTED_PLATFORMS[platform_name]['supported_content_types']
                if sanitized_filters["content_type"] not in supported_types:
                    continue

        # Location filter (enhanced with better matching)
        if sanitized_filters.get("location"):
            location_match = False
            filter_location = sanitized_filters["location"].lower()

            # Check profile location
            profile_location = profile.get("location", {})
            if isinstance(profile_location, dict):
                city = profile_location.get("city", "").lower()
                country = profile_location.get("country", "").lower()
                if filter_location in city or filter_location in country:
                    location_match = True

            # Check audience locations
            audience = profile_data.get("audience", {})
            audience_locations = audience.get("locations", [])
            for loc in audience_locations:
                if isinstance(loc, dict):
                    loc_name = loc.get("location_name", "").lower()
                    if filter_location in loc_name:
                        location_match = True
                        break

            if not location_match:
                continue

        # Interest filter (enhanced with better matching)
        if sanitized_filters.get("interests") and sanitized_filters["interests"]:
            interests_match = False

            # Get profile interests from multiple sources
            profile_interests = []

            # From top_interests
            top_interests = profile_data.get("top_interests", [])
            for interest in top_interests:
                if isinstance(interest, dict):
                    profile_interests.append(interest.get("name", "").lower())
                elif isinstance(interest, str):
                    profile_interests.append(interest.lower())

            # From audience interests
            audience = profile_data.get("audience", {})
            audience_interests = audience.get("interests", [])
            for interest in audience_interests:
                if isinstance(interest, dict):
                    profile_interests.append(interest.get("interest_name", "").lower())

            # Check if any filter interest matches profile interests
            for filter_interest in sanitized_filters["interests"]:
                filter_interest_lower = filter_interest.lower()
                for profile_interest in profile_interests:
                    if filter_interest_lower in profile_interest or profile_interest in filter_interest_lower:
                        interests_match = True
                        break
                if interests_match:
                    break

            if not interests_match:
                continue

        filtered.append(profile_data)

    return filtered

# API Endpoints

@phyllo_router.post("/profile/analytics")
async def get_profile_analytics(request: ProfileAnalyticsRequest):
    """
    Get detailed analytics for a creator profile.
    This endpoint mimics Phyllo's profile analytics API with enhanced validation and security.
    """
    try:
        # Rate limiting check
        if not validate_rate_limits():
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

        # Additional validation for profile_id
        profile_id = sanitize_search_input(request.profile_id)
        if not profile_id:
            raise HTTPException(status_code=400, detail="Invalid profile ID")

        # Content safety validation
        content_errors = validate_content_safety(profile_id)
        if content_errors:
            raise HTTPException(
                status_code=422,
                detail={
                    "message": "Content validation failed",
                    "errors": content_errors
                }
            )

        profiles = get_profile_analytics_data()

        # Find the specific profile
        target_profile = None
        for profile_data in profiles:
            if profile_data.get("id") == profile_id:
                target_profile = profile_data
                break

        if not target_profile:
            raise HTTPException(
                status_code=404,
                detail=f"Profile with ID '{profile_id}' not found"
            )

        # Validate platform exists and is supported
        work_platform = target_profile.get("work_platform", {})
        platform_name = work_platform.get("name", "").lower()
        if platform_name and platform_name not in SUPPORTED_PLATFORMS:
            raise HTTPException(
                status_code=422,
                detail=f"Profile platform '{platform_name}' is not supported"
            )

        # Build response based on include flags with safe data access
        response_data = {
            "id": target_profile.get("id"),
            "work_platform": target_profile.get("work_platform", {}),
            "profile": target_profile.get("profile", {})
        }

        if request.include_audience and "audience" in target_profile:
            # Sanitize audience data before including
            audience_data = target_profile["audience"]
            if isinstance(audience_data, dict):
                response_data["audience"] = audience_data

        if request.include_content:
            profile = target_profile.get("profile", {})
            response_data["content"] = {
                "top_contents": profile.get("top_contents", []),
                "recent_contents": profile.get("recent_contents", [])
            }

        if request.include_pricing and "pricing" in target_profile:
            # Sanitize pricing data
            pricing_data = target_profile["pricing"]
            if isinstance(pricing_data, (list, dict)):
                response_data["pricing"] = pricing_data

        # Validate response data before returning
        validated_data = validate_response_data(response_data)

        return PhylloResponse(
            success=True,
            data=validated_data,
            message="Profile analytics retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        # Log error details but don't expose internal information
        raise HTTPException(status_code=500, detail="Internal server error occurred")

@phyllo_router.post("/profile/quick-search")
async def quick_search_profiles(request: QuickSearchRequest):
    """
    Quick search for creator profiles with basic filters.
    This endpoint mimics Phyllo's quick search API with enhanced validation.
    """
    try:
        # Validate request parameters
        filters = request.model_dump(exclude_unset=True)
        validation_errors = validate_search_request(filters)

        if validation_errors:
            raise create_error_response(422, "Validation failed", validation_errors)

        profiles = get_profile_analytics_data()

        # Apply filters with enhanced validation
        filtered_profiles = filter_profiles(profiles, filters)

        # Apply limit
        limited_profiles = filtered_profiles[:request.limit]

        # Build simplified response for quick search
        results = []
        for profile_data in limited_profiles:
            try:
                profile = profile_data.get("profile", {})
                work_platform = profile_data.get("work_platform", {})

                result = {
                    "id": profile_data.get("id"),
                    "platform": work_platform.get("name"),
                    "username": profile.get("platform_username"),
                    "full_name": profile.get("full_name"),
                    "follower_count": profile.get("follower_count", 0),
                    "engagement_rate": profile.get("engagement_rate", 0.0),
                    "is_verified": profile.get("is_verified", False),
                    "image_url": profile.get("image_url"),
                    "url": profile.get("url")
                }
                results.append(result)
            except Exception as profile_error:
                # Log error but continue with other profiles
                continue

        # Validate response data
        response_data = {
            "profiles": validate_response_data(results),
            "total_found": len(filtered_profiles),
            "returned": len(results),
            "limit": request.limit,
            "filters_applied": {k: v for k, v in filters.items() if v is not None and v != []}
        }

        return PhylloResponse(
            success=True,
            data=response_data,
            message=f"Found {len(filtered_profiles)} profiles matching criteria"
        )

    except HTTPException:
        raise
    except Exception as e:
        log_security_event("API_ERROR", f"Quick search error: {str(e)}")
        raise create_error_response(500, "Internal server error occurred")

@phyllo_router.post("/profile/search")
async def advanced_search_profiles(request: AdvancedSearchRequest):
    """
    Advanced search for creator profiles with comprehensive filters.
    This endpoint mimics Phyllo's advanced search API with enhanced validation.
    """
    try:
        # Validate request parameters
        filters = request.model_dump(exclude_unset=True)
        validation_errors = validate_search_request(filters)

        if validation_errors:
            raise create_error_response(422, "Validation failed", validation_errors)

        profiles = get_profile_analytics_data()

        # Apply filters with enhanced validation
        filtered_profiles = filter_profiles(profiles, filters)

        # Validate pagination parameters
        if request.offset < 0:
            raise create_error_response(422, "Offset cannot be negative")

        if request.offset >= len(filtered_profiles) and len(filtered_profiles) > 0:
            raise create_error_response(
                422,
                f"Offset {request.offset} exceeds total results {len(filtered_profiles)}"
            )

        # Apply pagination
        start_idx = request.offset
        end_idx = start_idx + request.limit
        paginated_profiles = filtered_profiles[start_idx:end_idx]

        # Build detailed response for advanced search
        results = []
        for profile_data in paginated_profiles:
            try:
                profile = profile_data.get("profile", {})
                work_platform = profile_data.get("work_platform", {})

                # Include more detailed information with safe access
                result = {
                    "id": profile_data.get("id"),
                    "work_platform": work_platform,
                    "profile": {
                        "external_id": profile.get("external_id"),
                        "platform_username": profile.get("platform_username"),
                        "url": profile.get("url"),
                        "image_url": profile.get("image_url"),
                        "full_name": profile.get("full_name"),
                        "introduction": profile.get("introduction"),
                        "follower_count": profile.get("follower_count", 0),
                        "engagement_rate": profile.get("engagement_rate", 0.0),
                        "is_verified": profile.get("is_verified", False),
                        "gender": profile.get("gender"),
                        "age_group": profile.get("age_group"),
                        "location": profile.get("location")
                    },
                    "top_interests": profile_data.get("top_interests", []),
                    "top_hashtags": profile_data.get("top_hashtags", [])[:5],  # Limit to top 5
                }

                # Include audience summary if available
                if "audience" in profile_data:
                    audience = profile_data["audience"]
                    result["audience_summary"] = {
                        "top_locations": audience.get("locations", [])[:3],
                        "primary_language": audience.get("languages", [{}])[0] if audience.get("languages") else None,
                        "device_breakdown": audience.get("devices", [])
                    }

                results.append(result)
            except Exception as profile_error:
                # Log error but continue with other profiles
                continue

        # Pagination info
        has_next = end_idx < len(filtered_profiles)
        has_prev = start_idx > 0

        # Validate response data
        validated_results = validate_response_data(results)

        return PaginatedResponse(
            success=True,
            data=validated_results,
            pagination={
                "offset": request.offset,
                "limit": request.limit,
                "total": len(filtered_profiles),
                "returned": len(results),
                "has_next": has_next,
                "has_prev": has_prev,
                "filters_applied": {k: v for k, v in filters.items() if v is not None and v != []}
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        log_security_event("API_ERROR", f"Advanced search error: {str(e)}")
        raise create_error_response(500, "Internal server error occurred")

# Data Insertion Endpoints

class BulkProfileInsertRequest(BaseModel):
    profiles: List[Dict[str, Any]] = Field(..., description="List of profile analytics data to insert")
    batch_size: int = Field(50, description="Batch size for processing", ge=1, le=100)
    validate_only: bool = Field(False, description="Only validate data without inserting")

class SingleProfileInsertRequest(BaseModel):
    profile_data: Dict[str, Any] = Field(..., description="Profile analytics data to insert")
    overwrite: bool = Field(False, description="Overwrite existing profile if it exists")

@phyllo_router.post("/profile/insert")
async def insert_single_profile(request: SingleProfileInsertRequest):
    """
    Insert a single profile analytics record.
    This provides an API alternative to the bulk insert script.
    """
    try:
        memory_db = get_memory_db()

        profile_data = request.profile_data
        profile_id = profile_data.get("id")

        if not profile_id:
            raise HTTPException(status_code=400, detail="Profile ID is required")

        # Check if profile already exists
        existing_data = memory_db.get("data", {})
        profile_exists = False

        for data_id, data_entry in existing_data.items():
            if (data_entry.get("data_type") == "profile_analytics" and
                data_entry.get("content", {}).get("id") == profile_id):
                profile_exists = True
                if not request.overwrite:
                    raise HTTPException(
                        status_code=409,
                        detail=f"Profile {profile_id} already exists. Use overwrite=true to replace."
                    )
                # Remove existing entry for overwrite
                del existing_data[data_id]
                break

        # Validate profile data structure
        required_fields = ["id", "work_platform", "profile"]
        for field in required_fields:
            if field not in profile_data:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

        # Create new data entry
        data_id = f"data_{uuid.uuid4().hex[:8]}"
        account_id = f"account_{uuid.uuid4().hex[:8]}"

        new_entry = {
            "id": data_id,
            "account_id": account_id,
            "data_type": "profile_analytics",
            "content": profile_data,
            "created_at": datetime.now().isoformat()
        }

        # Insert into memory database
        if "data" not in memory_db:
            memory_db["data"] = {}
        memory_db["data"][data_id] = new_entry

        return PhylloResponse(
            success=True,
            data={
                "profile_id": profile_id,
                "data_id": data_id,
                "action": "updated" if profile_exists else "created",
                "message": f"Profile {'updated' if profile_exists else 'inserted'} successfully"
            },
            message="Profile data inserted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@phyllo_router.post("/profile/bulk-insert")
async def bulk_insert_profiles(request: BulkProfileInsertRequest):
    """
    Bulk insert multiple profile analytics records.
    This provides an API alternative to the bulk insert script.
    """
    try:
        memory_db = get_memory_db()

        if request.validate_only:
            # Validation mode - check data structure without inserting
            validation_results = []

            for i, profile_data in enumerate(request.profiles):
                result = {"index": i, "valid": True, "errors": []}

                # Check required fields
                required_fields = ["id", "work_platform", "profile"]
                for field in required_fields:
                    if field not in profile_data:
                        result["valid"] = False
                        result["errors"].append(f"Missing required field: {field}")

                # Check profile ID uniqueness
                profile_id = profile_data.get("id")
                if profile_id:
                    for j, other_profile in enumerate(request.profiles):
                        if j != i and other_profile.get("id") == profile_id:
                            result["valid"] = False
                            result["errors"].append(f"Duplicate profile ID: {profile_id}")
                            break

                validation_results.append(result)

            valid_count = sum(1 for r in validation_results if r["valid"])

            return PhylloResponse(
                success=True,
                data={
                    "validation_results": validation_results,
                    "summary": {
                        "total": len(request.profiles),
                        "valid": valid_count,
                        "invalid": len(request.profiles) - valid_count
                    }
                },
                message="Validation completed"
            )

        # Actual insertion mode
        results = {
            "successful": [],
            "failed": [],
            "updated": []
        }

        existing_data = memory_db.get("data", {})

        for i, profile_data in enumerate(request.profiles):
            try:
                profile_id = profile_data.get("id")

                if not profile_id:
                    results["failed"].append({
                        "index": i,
                        "profile_id": None,
                        "error": "Missing profile ID"
                    })
                    continue

                # Check if profile exists
                profile_exists = False
                existing_data_id = None

                for data_id, data_entry in existing_data.items():
                    if (data_entry.get("data_type") == "profile_analytics" and
                        data_entry.get("content", {}).get("id") == profile_id):
                        profile_exists = True
                        existing_data_id = data_id
                        break

                # Create or update entry
                if profile_exists:
                    # Update existing
                    existing_data[existing_data_id]["content"] = profile_data
                    existing_data[existing_data_id]["updated_at"] = datetime.now().isoformat()
                    results["updated"].append({
                        "index": i,
                        "profile_id": profile_id,
                        "data_id": existing_data_id
                    })
                else:
                    # Create new
                    data_id = f"data_{uuid.uuid4().hex[:8]}"
                    account_id = f"account_{uuid.uuid4().hex[:8]}"

                    new_entry = {
                        "id": data_id,
                        "account_id": account_id,
                        "data_type": "profile_analytics",
                        "content": profile_data,
                        "created_at": datetime.now().isoformat()
                    }

                    if "data" not in memory_db:
                        memory_db["data"] = {}
                    memory_db["data"][data_id] = new_entry

                    results["successful"].append({
                        "index": i,
                        "profile_id": profile_id,
                        "data_id": data_id
                    })

            except Exception as e:
                results["failed"].append({
                    "index": i,
                    "profile_id": profile_data.get("id"),
                    "error": str(e)
                })

        summary = {
            "total": len(request.profiles),
            "successful": len(results["successful"]),
            "updated": len(results["updated"]),
            "failed": len(results["failed"])
        }

        return PhylloResponse(
            success=True,
            data={
                "results": results,
                "summary": summary
            },
            message=f"Bulk insert completed: {summary['successful']} created, {summary['updated']} updated, {summary['failed']} failed"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@phyllo_router.post("/profile/bulk-insert-postgres")
async def bulk_insert_profiles_postgres(request: BulkProfileInsertRequest):
    """
    Bulk insert multiple profile analytics records into PostgreSQL.
    This uses the BulkInsertService for PostgreSQL insertion.
    """
    try:
        from ..services.bulk_insert_service import BulkInsertService
        from ..database_connection import db_manager

        if not db_manager.is_postgres_available():
            raise HTTPException(
                status_code=503,
                detail="PostgreSQL database not available. Use /profile/bulk-insert for memory database."
            )

        if request.validate_only:
            # Validation mode
            validation_results = []

            for i, profile_data in enumerate(request.profiles):
                result = {"index": i, "valid": True, "errors": []}

                # Check required fields
                required_fields = ["id", "work_platform", "profile"]
                for field in required_fields:
                    if field not in profile_data:
                        result["valid"] = False
                        result["errors"].append(f"Missing required field: {field}")

                validation_results.append(result)

            valid_count = sum(1 for r in validation_results if r["valid"])

            return PhylloResponse(
                success=True,
                data={
                    "validation_results": validation_results,
                    "summary": {
                        "total": len(request.profiles),
                        "valid": valid_count,
                        "invalid": len(request.profiles) - valid_count
                    }
                },
                message="Validation completed"
            )

        # Use the bulk insert service
        service = BulkInsertService()

        # Create a temporary JSON file with the profiles
        import tempfile
        import json

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            json.dump(request.profiles, temp_file, indent=2)
            temp_filepath = temp_file.name

        try:
            # Run bulk insert
            stats = service.bulk_insert_profiles(
                filepath=temp_filepath,
                limit=len(request.profiles)
            )

            return PhylloResponse(
                success=True,
                data={
                    "insertion_stats": stats,
                    "summary": {
                        "total": stats.get("processed", 0),
                        "successful": stats.get("successful", 0),
                        "failed": stats.get("failed", 0),
                        "duration": str(stats.get("final_counts", {}))
                    }
                },
                message=f"PostgreSQL bulk insert completed: {stats.get('successful', 0)} successful, {stats.get('failed', 0)} failed"
            )

        finally:
            # Clean up temporary file
            import os
            try:
                os.unlink(temp_filepath)
            except:
                pass

    except ImportError:
        raise HTTPException(
            status_code=503,
            detail="Bulk insert service not available. PostgreSQL dependencies may be missing."
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
