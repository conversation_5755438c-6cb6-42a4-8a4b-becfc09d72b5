"""
Phyllo-compatible API endpoints.
These endpoints mimic the structure and responses of Phyllo's actual API.
"""

from fastapi import APIRouter, HTTPException, Query, Path, Depends, Body
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
import json
import uuid
from .database_connection import get_db, get_memory_db
from .phyllo_models import (
    PhylloResponse, PaginatedResponse, ProfileSearchRequest,
    ProfileAnalytics, PhylloProfile, PhylloWorkPlatform
)

# Create router for Phyllo-compatible endpoints
phyllo_router = APIRouter(prefix="/v1/social/creator")

# Request/Response models for the specific endpoints
class ProfileAnalyticsRequest(BaseModel):
    profile_id: str = Field(..., description="The profile ID to get analytics for")
    include_audience: bool = Field(True, description="Include audience demographics")
    include_content: bool = Field(True, description="Include top content")
    include_pricing: bool = Field(True, description="Include pricing information")

class QuickSearchRequest(BaseModel):
    platform: Optional[str] = Field(None, description="Platform to search (instagram, youtube, tiktok, etc.)")
    username: Optional[str] = Field(None, description="Username to search for")
    follower_count_min: Optional[int] = Field(None, description="Minimum follower count")
    follower_count_max: Optional[int] = Field(None, description="Maximum follower count")
    engagement_rate_min: Optional[float] = Field(None, description="Minimum engagement rate")
    engagement_rate_max: Optional[float] = Field(None, description="Maximum engagement rate")
    verified_only: Optional[bool] = Field(False, description="Only verified accounts")
    limit: int = Field(10, description="Number of results to return", ge=1, le=100)

class AdvancedSearchRequest(BaseModel):
    platform: Optional[str] = Field(None, description="Platform to search")
    username: Optional[str] = Field(None, description="Username to search for")
    follower_count_min: Optional[int] = Field(None, description="Minimum follower count")
    follower_count_max: Optional[int] = Field(None, description="Maximum follower count")
    engagement_rate_min: Optional[float] = Field(None, description="Minimum engagement rate")
    engagement_rate_max: Optional[float] = Field(None, description="Maximum engagement rate")
    interests: Optional[List[str]] = Field([], description="List of interests to filter by")
    location: Optional[str] = Field(None, description="Location to filter by")
    gender: Optional[str] = Field(None, description="Gender to filter by (MALE/FEMALE)")
    age_group: Optional[str] = Field(None, description="Age group to filter by")
    verified_only: Optional[bool] = Field(False, description="Only verified accounts")
    content_type: Optional[str] = Field(None, description="Content type preference")
    limit: int = Field(10, description="Number of results to return", ge=1, le=100)
    offset: int = Field(0, description="Offset for pagination", ge=0)

# Helper functions
def get_profile_analytics_data():
    """Get profile analytics data from memory database"""
    memory_db = get_memory_db()
    data_entries = list(memory_db.get("data", {}).values())
    
    # Filter for profile analytics data
    profile_analytics = []
    for entry in data_entries:
        if entry.get("data_type") == "profile_analytics":
            profile_analytics.append(entry["content"])
    
    return profile_analytics

def filter_profiles(profiles: List[Dict], filters: Dict) -> List[Dict]:
    """Apply filters to profile list"""
    filtered = []
    
    for profile_data in profiles:
        profile = profile_data.get("profile", {})
        work_platform = profile_data.get("work_platform", {})
        
        # Platform filter
        if filters.get("platform"):
            platform_name = work_platform.get("name", "").lower()
            if filters["platform"].lower() not in platform_name:
                continue
        
        # Username filter
        if filters.get("username"):
            username = profile.get("platform_username", "").lower()
            if filters["username"].lower() not in username:
                continue
        
        # Follower count filters
        follower_count = profile.get("follower_count", 0)
        if filters.get("follower_count_min") and follower_count < filters["follower_count_min"]:
            continue
        if filters.get("follower_count_max") and follower_count > filters["follower_count_max"]:
            continue
        
        # Engagement rate filters
        engagement_rate = profile.get("engagement_rate", 0)
        if filters.get("engagement_rate_min") and engagement_rate < filters["engagement_rate_min"]:
            continue
        if filters.get("engagement_rate_max") and engagement_rate > filters["engagement_rate_max"]:
            continue
        
        # Verified filter
        if filters.get("verified_only") and not profile.get("is_verified", False):
            continue
        
        # Gender filter
        if filters.get("gender") and profile.get("gender") != filters["gender"]:
            continue
        
        # Age group filter
        if filters.get("age_group") and profile.get("age_group") != filters["age_group"]:
            continue
        
        # Location filter (check both profile location and audience locations)
        if filters.get("location"):
            location_match = False
            
            # Check profile location
            profile_location = profile.get("location", {})
            if (filters["location"].lower() in profile_location.get("city", "").lower() or
                filters["location"].lower() in profile_location.get("country", "").lower()):
                location_match = True
            
            # Check audience locations
            audience = profile_data.get("audience", {})
            audience_locations = audience.get("locations", [])
            for loc in audience_locations:
                if filters["location"].lower() in loc.get("country", "").lower():
                    location_match = True
                    break
            
            if not location_match:
                continue
        
        # Interest filter
        if filters.get("interests"):
            interests_match = False
            top_interests = profile.get("top_interests", [])
            profile_interests = [interest.get("name", "").lower() for interest in top_interests]
            
            for filter_interest in filters["interests"]:
                if any(filter_interest.lower() in pi for pi in profile_interests):
                    interests_match = True
                    break
            
            if not interests_match:
                continue
        
        filtered.append(profile_data)
    
    return filtered

# API Endpoints

@phyllo_router.post("/profile/analytics")
async def get_profile_analytics(request: ProfileAnalyticsRequest):
    """
    Get detailed analytics for a creator profile.
    This endpoint mimics Phyllo's profile analytics API.
    """
    try:
        profiles = get_profile_analytics_data()
        
        # Find the specific profile
        target_profile = None
        for profile_data in profiles:
            if profile_data.get("id") == request.profile_id:
                target_profile = profile_data
                break
        
        if not target_profile:
            raise HTTPException(status_code=404, detail="Profile not found")
        
        # Build response based on include flags
        response_data = {
            "id": target_profile["id"],
            "work_platform": target_profile["work_platform"],
            "profile": target_profile["profile"]
        }
        
        if request.include_audience and "audience" in target_profile:
            response_data["audience"] = target_profile["audience"]
        
        if request.include_content:
            profile = target_profile["profile"]
            response_data["content"] = {
                "top_contents": profile.get("top_contents", []),
                "recent_contents": profile.get("recent_contents", [])
            }
        
        if request.include_pricing and "pricing" in target_profile:
            response_data["pricing"] = target_profile["pricing"]
        
        return PhylloResponse(
            success=True,
            data=response_data,
            message="Profile analytics retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@phyllo_router.post("/profile/quick-search")
async def quick_search_profiles(request: QuickSearchRequest):
    """
    Quick search for creator profiles with basic filters.
    This endpoint mimics Phyllo's quick search API.
    """
    try:
        profiles = get_profile_analytics_data()
        
        # Apply filters
        filters = request.model_dump(exclude_unset=True)
        filtered_profiles = filter_profiles(profiles, filters)
        
        # Apply limit
        limited_profiles = filtered_profiles[:request.limit]
        
        # Build simplified response for quick search
        results = []
        for profile_data in limited_profiles:
            profile = profile_data["profile"]
            results.append({
                "id": profile_data["id"],
                "platform": profile_data["work_platform"]["name"],
                "username": profile["platform_username"],
                "full_name": profile["full_name"],
                "follower_count": profile["follower_count"],
                "engagement_rate": profile["engagement_rate"],
                "is_verified": profile["is_verified"],
                "image_url": profile["image_url"],
                "url": profile["url"]
            })
        
        return PhylloResponse(
            success=True,
            data={
                "profiles": results,
                "total_found": len(filtered_profiles),
                "returned": len(results),
                "limit": request.limit
            },
            message=f"Found {len(filtered_profiles)} profiles"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@phyllo_router.post("/profile/search")
async def advanced_search_profiles(request: AdvancedSearchRequest):
    """
    Advanced search for creator profiles with comprehensive filters.
    This endpoint mimics Phyllo's advanced search API.
    """
    try:
        profiles = get_profile_analytics_data()
        
        # Apply filters
        filters = request.model_dump(exclude_unset=True)
        filtered_profiles = filter_profiles(profiles, filters)
        
        # Apply pagination
        start_idx = request.offset
        end_idx = start_idx + request.limit
        paginated_profiles = filtered_profiles[start_idx:end_idx]
        
        # Build detailed response for advanced search
        results = []
        for profile_data in paginated_profiles:
            profile = profile_data["profile"]
            
            # Include more detailed information
            result = {
                "id": profile_data["id"],
                "work_platform": profile_data["work_platform"],
                "profile": {
                    "external_id": profile["external_id"],
                    "platform_username": profile["platform_username"],
                    "url": profile["url"],
                    "image_url": profile["image_url"],
                    "full_name": profile["full_name"],
                    "introduction": profile["introduction"],
                    "follower_count": profile["follower_count"],
                    "engagement_rate": profile["engagement_rate"],
                    "is_verified": profile["is_verified"],
                    "gender": profile["gender"],
                    "age_group": profile["age_group"],
                    "location": profile["location"]
                },
                "top_interests": profile.get("top_interests", []),
                "top_hashtags": profile.get("top_hashtags", [])[:5],  # Limit to top 5
            }
            
            # Include audience summary if available
            if "audience" in profile_data:
                audience = profile_data["audience"]
                result["audience_summary"] = {
                    "top_locations": audience.get("locations", [])[:3],
                    "primary_language": audience.get("languages", [{}])[0] if audience.get("languages") else None,
                    "device_breakdown": audience.get("devices", [])
                }
            
            results.append(result)
        
        # Pagination info
        has_next = end_idx < len(filtered_profiles)
        has_prev = start_idx > 0
        
        return PaginatedResponse(
            success=True,
            data=results,
            pagination={
                "offset": request.offset,
                "limit": request.limit,
                "total": len(filtered_profiles),
                "returned": len(results),
                "has_next": has_next,
                "has_prev": has_prev
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Data Insertion Endpoints

class BulkProfileInsertRequest(BaseModel):
    profiles: List[Dict[str, Any]] = Field(..., description="List of profile analytics data to insert")
    batch_size: int = Field(50, description="Batch size for processing", ge=1, le=100)
    validate_only: bool = Field(False, description="Only validate data without inserting")

class SingleProfileInsertRequest(BaseModel):
    profile_data: Dict[str, Any] = Field(..., description="Profile analytics data to insert")
    overwrite: bool = Field(False, description="Overwrite existing profile if it exists")

@phyllo_router.post("/profile/insert")
async def insert_single_profile(request: SingleProfileInsertRequest):
    """
    Insert a single profile analytics record.
    This provides an API alternative to the bulk insert script.
    """
    try:
        memory_db = get_memory_db()

        profile_data = request.profile_data
        profile_id = profile_data.get("id")

        if not profile_id:
            raise HTTPException(status_code=400, detail="Profile ID is required")

        # Check if profile already exists
        existing_data = memory_db.get("data", {})
        profile_exists = False

        for data_id, data_entry in existing_data.items():
            if (data_entry.get("data_type") == "profile_analytics" and
                data_entry.get("content", {}).get("id") == profile_id):
                profile_exists = True
                if not request.overwrite:
                    raise HTTPException(
                        status_code=409,
                        detail=f"Profile {profile_id} already exists. Use overwrite=true to replace."
                    )
                # Remove existing entry for overwrite
                del existing_data[data_id]
                break

        # Validate profile data structure
        required_fields = ["id", "work_platform", "profile"]
        for field in required_fields:
            if field not in profile_data:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

        # Create new data entry
        data_id = f"data_{uuid.uuid4().hex[:8]}"
        account_id = f"account_{uuid.uuid4().hex[:8]}"

        new_entry = {
            "id": data_id,
            "account_id": account_id,
            "data_type": "profile_analytics",
            "content": profile_data,
            "created_at": datetime.now().isoformat()
        }

        # Insert into memory database
        if "data" not in memory_db:
            memory_db["data"] = {}
        memory_db["data"][data_id] = new_entry

        return PhylloResponse(
            success=True,
            data={
                "profile_id": profile_id,
                "data_id": data_id,
                "action": "updated" if profile_exists else "created",
                "message": f"Profile {'updated' if profile_exists else 'inserted'} successfully"
            },
            message="Profile data inserted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@phyllo_router.post("/profile/bulk-insert")
async def bulk_insert_profiles(request: BulkProfileInsertRequest):
    """
    Bulk insert multiple profile analytics records.
    This provides an API alternative to the bulk insert script.
    """
    try:
        memory_db = get_memory_db()

        if request.validate_only:
            # Validation mode - check data structure without inserting
            validation_results = []

            for i, profile_data in enumerate(request.profiles):
                result = {"index": i, "valid": True, "errors": []}

                # Check required fields
                required_fields = ["id", "work_platform", "profile"]
                for field in required_fields:
                    if field not in profile_data:
                        result["valid"] = False
                        result["errors"].append(f"Missing required field: {field}")

                # Check profile ID uniqueness
                profile_id = profile_data.get("id")
                if profile_id:
                    for j, other_profile in enumerate(request.profiles):
                        if j != i and other_profile.get("id") == profile_id:
                            result["valid"] = False
                            result["errors"].append(f"Duplicate profile ID: {profile_id}")
                            break

                validation_results.append(result)

            valid_count = sum(1 for r in validation_results if r["valid"])

            return PhylloResponse(
                success=True,
                data={
                    "validation_results": validation_results,
                    "summary": {
                        "total": len(request.profiles),
                        "valid": valid_count,
                        "invalid": len(request.profiles) - valid_count
                    }
                },
                message="Validation completed"
            )

        # Actual insertion mode
        results = {
            "successful": [],
            "failed": [],
            "updated": []
        }

        existing_data = memory_db.get("data", {})

        for i, profile_data in enumerate(request.profiles):
            try:
                profile_id = profile_data.get("id")

                if not profile_id:
                    results["failed"].append({
                        "index": i,
                        "profile_id": None,
                        "error": "Missing profile ID"
                    })
                    continue

                # Check if profile exists
                profile_exists = False
                existing_data_id = None

                for data_id, data_entry in existing_data.items():
                    if (data_entry.get("data_type") == "profile_analytics" and
                        data_entry.get("content", {}).get("id") == profile_id):
                        profile_exists = True
                        existing_data_id = data_id
                        break

                # Create or update entry
                if profile_exists:
                    # Update existing
                    existing_data[existing_data_id]["content"] = profile_data
                    existing_data[existing_data_id]["updated_at"] = datetime.now().isoformat()
                    results["updated"].append({
                        "index": i,
                        "profile_id": profile_id,
                        "data_id": existing_data_id
                    })
                else:
                    # Create new
                    data_id = f"data_{uuid.uuid4().hex[:8]}"
                    account_id = f"account_{uuid.uuid4().hex[:8]}"

                    new_entry = {
                        "id": data_id,
                        "account_id": account_id,
                        "data_type": "profile_analytics",
                        "content": profile_data,
                        "created_at": datetime.now().isoformat()
                    }

                    if "data" not in memory_db:
                        memory_db["data"] = {}
                    memory_db["data"][data_id] = new_entry

                    results["successful"].append({
                        "index": i,
                        "profile_id": profile_id,
                        "data_id": data_id
                    })

            except Exception as e:
                results["failed"].append({
                    "index": i,
                    "profile_id": profile_data.get("id"),
                    "error": str(e)
                })

        summary = {
            "total": len(request.profiles),
            "successful": len(results["successful"]),
            "updated": len(results["updated"]),
            "failed": len(results["failed"])
        }

        return PhylloResponse(
            success=True,
            data={
                "results": results,
                "summary": summary
            },
            message=f"Bulk insert completed: {summary['successful']} created, {summary['updated']} updated, {summary['failed']} failed"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@phyllo_router.post("/profile/bulk-insert-postgres")
async def bulk_insert_profiles_postgres(request: BulkProfileInsertRequest):
    """
    Bulk insert multiple profile analytics records into PostgreSQL.
    This uses the BulkInsertService for PostgreSQL insertion.
    """
    try:
        from ..services.bulk_insert_service import BulkInsertService
        from ..database_connection import db_manager

        if not db_manager.is_postgres_available():
            raise HTTPException(
                status_code=503,
                detail="PostgreSQL database not available. Use /profile/bulk-insert for memory database."
            )

        if request.validate_only:
            # Validation mode
            validation_results = []

            for i, profile_data in enumerate(request.profiles):
                result = {"index": i, "valid": True, "errors": []}

                # Check required fields
                required_fields = ["id", "work_platform", "profile"]
                for field in required_fields:
                    if field not in profile_data:
                        result["valid"] = False
                        result["errors"].append(f"Missing required field: {field}")

                validation_results.append(result)

            valid_count = sum(1 for r in validation_results if r["valid"])

            return PhylloResponse(
                success=True,
                data={
                    "validation_results": validation_results,
                    "summary": {
                        "total": len(request.profiles),
                        "valid": valid_count,
                        "invalid": len(request.profiles) - valid_count
                    }
                },
                message="Validation completed"
            )

        # Use the bulk insert service
        service = BulkInsertService()

        # Create a temporary JSON file with the profiles
        import tempfile
        import json

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            json.dump(request.profiles, temp_file, indent=2)
            temp_filepath = temp_file.name

        try:
            # Run bulk insert
            stats = service.bulk_insert_profiles(
                filepath=temp_filepath,
                limit=len(request.profiles)
            )

            return PhylloResponse(
                success=True,
                data={
                    "insertion_stats": stats,
                    "summary": {
                        "total": stats.get("processed", 0),
                        "successful": stats.get("successful", 0),
                        "failed": stats.get("failed", 0),
                        "duration": str(stats.get("final_counts", {}))
                    }
                },
                message=f"PostgreSQL bulk insert completed: {stats.get('successful', 0)} successful, {stats.get('failed', 0)} failed"
            )

        finally:
            # Clean up temporary file
            import os
            try:
                os.unlink(temp_filepath)
            except:
                pass

    except ImportError:
        raise HTTPException(
            status_code=503,
            detail="Bulk insert service not available. PostgreSQL dependencies may be missing."
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
