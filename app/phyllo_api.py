"""
Phyllo-compatible API endpoints.
These endpoints mimic the structure and responses of Phyllo's actual API.
"""

from fastapi import APIRouter, HTTPException, Query, Path, Depends, Body
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator, model_validator
from datetime import datetime
import json
import uuid
import re
import random
from .database_connection import get_db, get_memory_db
from .phyllo_models import (
    PhylloResponse, PaginatedResponse, ProfileSearchRequest,
    ProfileAnalytics, PhylloProfile, PhylloWorkPlatform
)

# Create router for Phyllo-compatible endpoints
phyllo_router = APIRouter(prefix="/v1/social/creator")



# Supported platforms and their constraints
SUPPORTED_PLATFORMS = {
    "instagram": {
        "name": "Instagram",
        "max_follower_count": 500_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["REEL", "STORY", "STATIC_POST", "CAROUSEL"],
        "username_pattern": r"^[a-zA-Z0-9._]{1,30}$"
    },
    "youtube": {
        "name": "YouTube",
        "max_follower_count": 200_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["VIDEO"],
        "username_pattern": r"^[a-zA-Z0-9._-]{1,100}$"
    },
    "tiktok": {
        "name": "TikTok",
        "max_follower_count": 150_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["VIDEO", "REEL"],
        "username_pattern": r"^[a-zA-Z0-9._]{1,24}$"
    },
    "twitter": {
        "name": "Twitter",
        "max_follower_count": 130_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["STATIC_POST"],
        "username_pattern": r"^[a-zA-Z0-9_]{1,15}$"
    },
    "twitch": {
        "name": "Twitch",
        "max_follower_count": 20_000_000,
        "max_engagement_rate": 1.0,
        "supported_content_types": ["VIDEO"],
        "username_pattern": r"^[a-zA-Z0-9_]{4,25}$"
    }
}

VALID_GENDERS = ["MALE", "FEMALE"]
VALID_AGE_GROUPS = ["13-17", "18-24", "25-34", "35-44", "45-54", "55-64", "65+"]
VALID_CONTENT_TYPES = ["VIDEO", "IMAGE", "REEL", "STORY", "STATIC_POST", "CAROUSEL"]

# Request/Response models for the specific endpoints
class ProfileAnalyticsRequest(BaseModel):
    profile_id: str = Field(..., description="The profile ID to get analytics for", min_length=1, max_length=100)
    include_audience: bool = Field(True, description="Include audience demographics")
    include_content: bool = Field(True, description="Include top content")
    include_pricing: bool = Field(True, description="Include pricing information")

    @field_validator('profile_id')
    @classmethod
    def validate_profile_id(cls, v):
        if not v or not v.strip():
            raise ValueError('Profile ID cannot be empty')
        # Basic UUID format validation (flexible to support different ID formats)
        if len(v.strip()) < 8:
            raise ValueError('Profile ID must be at least 8 characters long')
        return v.strip()

# Exact Phyllo API models based on documentation

class FollowerCountFilter(BaseModel):
    min: Optional[int] = Field(None, description="Minimum followers")
    max: Optional[int] = Field(None, description="Maximum followers")

class SubscriberCountFilter(BaseModel):
    min: Optional[int] = Field(None, description="Minimum subscribers")
    max: Optional[int] = Field(None, description="Maximum subscribers")

class ContentCountFilter(BaseModel):
    min: Optional[int] = Field(None, description="Minimum number of contents")
    max: Optional[int] = Field(None, description="Maximum number of contents")

class AudienceGenderFilter(BaseModel):
    type: str = Field(..., description="Gender type: ANY, FEMALE, MALE")
    operator: Optional[str] = Field(None, description="Comparison operator, required if the gender is not ANY")
    percentage_value: Optional[int] = Field(None, description="Percentage value for the comparison")

    @field_validator('type')
    @classmethod
    def validate_type(cls, v):
        allowed_values = ["ANY", "FEMALE", "MALE"]
        if v not in allowed_values:
            raise ValueError(f'Type must be one of: {", ".join(allowed_values)}')
        return v

    @field_validator('operator')
    @classmethod
    def validate_operator(cls, v):
        if v is not None and v != "GT":
            raise ValueError('Operator must be GT')
        return v

class AudienceAgeFilter(BaseModel):
    min: Optional[int] = Field(None, description="Minimum age of audience")
    max: Optional[int] = Field(None, description="Maximum age of audience")
    percentage_value: Optional[int] = Field(None, description="Filter by percentage of audience in the given age range", ge=0, le=100)

class CreatorAgeFilter(BaseModel):
    min: Optional[int] = Field(None, description="Minimum age of creator")
    max: Optional[int] = Field(None, description="Maximum age of creator")

class EngagementRateFilter(BaseModel):
    percentage_value: str = Field(..., description="Percentage value for the comparison, which would always be a 'greater than or equal to' comparison")

class SpecificContactDetailsFilter(BaseModel):
    type: str = Field(..., description="Contact type")
    preference: str = Field("SHOULD_HAVE", description="Contact preference")

    @field_validator('type')
    @classmethod
    def validate_type(cls, v):
        allowed_types = [
            "BBM", "EMAIL", "FACEBOOK", "INSTAGRAM", "ITUNES", "KAKAO", "KIK",
            "LINEID", "LINKTREE", "PHONE", "PINTEREST", "SARAHAH", "SAYAT",
            "SKYPE", "SNAPCHAT", "TELEGRAM", "TIKTOK", "TUMBLR", "TWITCHTV",
            "TWITTER", "VIBER", "VK", "WECHAT", "WEIBO", "WHATSAPP", "YOUTUBE"
        ]
        if v not in allowed_types:
            raise ValueError(f'Type must be one of: {", ".join(allowed_types)}')
        return v

    @field_validator('preference')
    @classmethod
    def validate_preference(cls, v):
        allowed_preferences = ["MUST_HAVE", "SHOULD_HAVE"]
        if v not in allowed_preferences:
            raise ValueError(f'Preference must be one of: {", ".join(allowed_preferences)}')
        return v

class AudienceLanguageFilter(BaseModel):
    code: str = Field(..., description="2 letter language code as per ISO 639-1")
    percentage_value: str = Field(..., description="Percentage value for the comparison, which would always be a 'greater than or equal to' comparison")

class CreatorLanguageFilter(BaseModel):
    code: Optional[str] = Field(None, description="2 letter language code as per ISO 639-1")

class AudienceInterestAffinitiesFilter(BaseModel):
    value: str = Field(..., description="Name of the interest")
    operation: str = Field(..., description="Name of the operation")
    percentage_value: str = Field(..., description="Percentage value")

    @field_validator('operation')
    @classmethod
    def validate_operation(cls, v):
        if v != "GT":
            raise ValueError('Operation must be GT')
        return v

class AverageFilter(BaseModel):
    min: Optional[int] = Field(None, description="Minimum value")
    max: Optional[int] = Field(None, description="Maximum value")

class InstagramReelViewsFilter(BaseModel):
    min: Optional[int] = Field(None, description="Minimum reel views")
    max: Optional[int] = Field(None, description="Maximum reel views")

class InstagramOptionsFilter(BaseModel):
    reel_views: Optional[InstagramReelViewsFilter] = Field(None, description="Filter profiles by average reels views")

class AudienceLocationFilter(BaseModel):
    location_id: str = Field(..., description="Phyllo's location identifier")
    percentage_value: Optional[float] = Field(None, description="Percentage value for the comparison")
    operator: str = Field("GT", description="Comparison operator")

    @field_validator('operator')
    @classmethod
    def validate_operator(cls, v):
        if v != "GT":
            raise ValueError('Operator must be GT')
        return v

class GrowthFilter(BaseModel):
    interval: int = Field(..., description="Time interval of growth")
    interval_unit: str = Field(..., description="Unit of time interval")
    operator: str = Field(..., description="Comparison operator")
    percentage_value: int = Field(..., description="Percentage value for the comparison")

    @field_validator('interval_unit')
    @classmethod
    def validate_interval_unit(cls, v):
        if v != "MONTH":
            raise ValueError('Interval unit must be MONTH')
        return v

    @field_validator('operator')
    @classmethod
    def validate_operator(cls, v):
        if v != "GT":
            raise ValueError('Operator must be GT')
        return v

class HashtagFilter(BaseModel):
    name: str = Field(..., description="Please provide hashtags without # prefix")

class MentionFilter(BaseModel):
    name: str = Field(..., description="Please provide mentions without @ prefix")

class TopicRelevanceFilter(BaseModel):
    name: List[str] = Field(..., description="Pass the topic name")
    weight: float = Field(0.5, description="Weight for topic relevance")
    threshold: float = Field(0.55, description="Threshold for topic relevance")

class LegacyAudienceLocationFilter(BaseModel):
    name: str = Field(..., description="Name of the location")
    percentage_value: int = Field(..., description="Percentage value for the comparison")
    operator: str = Field("GT", description="Comparison operator")

    @field_validator('operator')
    @classmethod
    def validate_operator(cls, v):
        if v != "GT":
            raise ValueError('Operator must be GT')
        return v

class SortByFilter(BaseModel):
    field: str = Field(..., description="Field to sort by")
    order: str = Field(..., description="Ordering pattern")

    @field_validator('field')
    @classmethod
    def validate_field(cls, v):
        allowed_fields = [
            "AVERAGE_LIKES", "FOLLOWER_COUNT", "ENGAGEMENT_RATE", "DESCRIPTION",
            "AVERAGE_VIEWS", "CONTENT_COUNT", "REELS_VIEWS", "FOLLOWER_GROWTH",
            "TOTAL_VIEWS_GROWTH", "TOTAL_LIKES_GROWTH", "AUDIENCE_LOCATIONS",
            "AUDIENCE_LANGUAGE", "AUDIENCE_BRAND_AFFINITIES", "AUDIENCE_INTERESTS",
            "AUDIENCE_AGE", "CREATOR_LOOKALIKES", "AUDIENCE_LOOKALIKES", "AVERAGE_LIKE",
            "AUDIENCE_LOCATION", "TOPIC_RELEVANCE", "PEAK_VIEWERS", "AVG_CONCURRENT_VIEWERS",
            "AIRTIME", "HOURS_WATCHED"
        ]
        if v not in allowed_fields:
            raise ValueError(f'Field must be one of: {", ".join(allowed_fields)}')
        return v

    @field_validator('order')
    @classmethod
    def validate_order(cls, v):
        if v != "DESCENDING":
            raise ValueError('Order must be DESCENDING')
        return v

class LivestreamOptionsFilter(BaseModel):
    recent_activity: Optional[str] = Field(None, description="Game/Category slug on Twitch")
    category: Optional[str] = Field(None, description="Twitch category")
    categories_streamed: Optional[str] = Field(None, description="Number of categories streamed")
    hours_watched: Optional[AverageFilter] = Field(None, description="Hours watched filter")
    average_concurrent_viewers: Optional[AverageFilter] = Field(None, description="Average concurrent viewers filter")
    absolute_follower_growth: Optional[AverageFilter] = Field(None, description="Absolute follower growth filter")
    peak_viewers: Optional[AverageFilter] = Field(None, description="Peak viewers filter")
    vtubers: Optional[Dict] = Field(None, description="VTubers filter")

    @field_validator('category')
    @classmethod
    def validate_category(cls, v):
        if v is not None:
            allowed_categories = ["IRL", "GAMING", "ESPORTS", "SPORTS", "MUSIC", "SLOTS", "CHESS"]
            if v not in allowed_categories:
                raise ValueError(f'Category must be one of: {", ".join(allowed_categories)}')
        return v

    @field_validator('categories_streamed')
    @classmethod
    def validate_categories_streamed(cls, v):
        if v is not None:
            allowed_values = ["1", "2-5", "6-10", "11-25", "25-1000"]
            if v not in allowed_values:
                raise ValueError(f'Categories streamed must be one of: {", ".join(allowed_values)}')
        return v

# Complete Phyllo Quick Search Request Model (Exact API Match)
class PhylloQuickSearchRequest(BaseModel):
    # Required parameters
    work_platform_id: str = Field(..., description="Target platform to search public profiles on")
    sort_by: SortByFilter = Field(..., description="Sorting order for the search results")

    # Optional filters - exactly as per Phyllo API
    follower_count: Optional[FollowerCountFilter] = Field(None, description="Filter profiles by follower count")
    subscriber_count: Optional[SubscriberCountFilter] = Field(None, description="Filter profiles by subscriber count")
    content_count: Optional[ContentCountFilter] = Field(None, description="Filter profiles by content count")
    audience_gender: Optional[AudienceGenderFilter] = Field(None, description="Filter profiles by audience gender")
    creator_gender: Optional[str] = Field(None, description="Filter profiles on the basis of the gender of the creator")
    audience_age: Optional[AudienceAgeFilter] = Field(None, description="Filter profiles by audience age")
    creator_age: Optional[CreatorAgeFilter] = Field(None, description="Filter profiles by creator age")
    description_keywords: Optional[str] = Field(None, description="Filter profiles by a list of keywords that can be found in profile bio/introduction/description/phrases in the video or captions for YouTube")
    is_verified: Optional[bool] = Field(None, description="Filter profiles by platform verification status")
    has_contact_details: Optional[bool] = Field(None, description="Filter profiles by availability of contact details")
    specific_contact_details: Optional[List[SpecificContactDetailsFilter]] = Field(None, description="Contact information")
    last_post_timestamp: Optional[str] = Field(None, description="Filter profiles by the timestamp (ISO 8601) of last post")
    audience_language: Optional[List[AudienceLanguageFilter]] = Field(None, description="Filter profiles by audience's language")
    creator_language: Optional[CreatorLanguageFilter] = Field(None, description="Filter profiles by creator's language")
    audience_interests: Optional[List[str]] = Field(None, description="List of interest names, which will return creators whose audience match even 1 of these interests. Only for Instagram")
    audience_interest_affinities: Optional[List[AudienceInterestAffinitiesFilter]] = Field(None, description="Audience interest affinities")
    creator_interests: Optional[List[str]] = Field(None, description="List of interest names, which will return creators whose audience match even 1 of these interests. Only for Instagram")
    audience_brand_affinities: Optional[List[str]] = Field(None, description="List of interest names, which will return creators whose audience match even 1 of these brand affinities. Only for Instagram")
    creator_brand_affinities: Optional[List[str]] = Field(None, description="List of interest names, which will return creators whose audience match even 1 of these brand affinities. Only for Instagram")
    average_likes: Optional[AverageFilter] = Field(None, description="Filter profiles by average likes")
    average_views: Optional[AverageFilter] = Field(None, description="Filter profiles by average views")
    engagement_rate: Optional[EngagementRateFilter] = Field(None, description="Filter profiles by engagement rates")
    has_sponsored_posts: Optional[bool] = Field(None, description="Filter profiles which have sponsored posts. Only for Instagram")
    brand_sponsors: Optional[List[str]] = Field(None, description="Filter profiles which have been sponsored by certain brands, which will return creators who match even 1 of these brands. Only for Instagram")
    instagram_options: Optional[InstagramOptionsFilter] = Field(None, description="Instagram specific options")
    audience_locations: Optional[List[AudienceLocationFilter]] = Field(None, description="Phyllo's location identifiers to be passed to this attribute")
    creator_locations: Optional[List[str]] = Field(None, description="Phyllo's location identifiers to be passed to this attribute")
    follower_growth: Optional[GrowthFilter] = Field(None, description="Filter profiles by follower growth rate")
    subscriber_growth: Optional[GrowthFilter] = Field(None, description="Filter profiles by subscriber growth rate")
    bio_phrase: Optional[str] = Field(None, description="Filter profiles by the phrase they use in their bio")
    hashtags: Optional[List[HashtagFilter]] = Field(None, description="Filter profiles by hashtags")
    mentions: Optional[List[MentionFilter]] = Field(None, description="Filter profiles by mentions")
    topic_relevance: Optional[TopicRelevanceFilter] = Field(None, description="Filter profiles by topic relevance")
    audience_lookalikes: Optional[str] = Field(None, description="Filter profiles whose posts look like the given handle/username. Please pass the social handle without the preceding @")
    platform_account_type: Optional[str] = Field(None, description="Filter profiles by account type")
    creator_account_type: Optional[List[str]] = Field(None, description="Filter profiles by creator account type")
    creator_lookalikes: Optional[str] = Field(None, description="Filter profiles whose posts look like the given handle/username. Please pass the social handle with the preceding @")
    audience_location: Optional[List[LegacyAudienceLocationFilter]] = Field(None, description="Avoid using this filter. Use the audience_locations filter instead")

    # Pagination and other options
    limit: int = Field(10, description="The number of search results to be returned. It can be between 1 to 500", ge=1, le=500)
    offset: int = Field(0, description="The number of search results to skip. Use this argument for pagination", ge=0)
    audience_source: str = Field("LIKERS", description="Filter profiles by source of the audience data")
    total_engagements: Optional[AverageFilter] = Field(None, description="Filter profiles by total number of engagements")
    audience_credibility_category: Optional[List[str]] = Field(None, description="Filter profiles according to the given credibility categories given")
    audience_credibility_score: Optional[float] = Field(None, description="Filter profiles according to the given credibility score or higher", ge=0, le=1)
    is_official_artist: bool = Field(False, description="Filter YouTube profiles which are official artists")
    has_audience_info: bool = Field(False, description="Fetches only profiles with audience info")
    share_count: Optional[AverageFilter] = Field(None, description="Filter TikTok profiles by count of shares")
    save_count: Optional[AverageFilter] = Field(None, description="Filter TikTok profiles by count of saves")
    exclude_private_profiles: bool = Field(False, description="Get only private profiles")
    creator_age_bracket: Optional[str] = Field(None, description="Only for Twitch")
    livestream_options: Optional[LivestreamOptionsFilter] = Field(None, description="Only for Twitch")

    @field_validator('creator_gender')
    @classmethod
    def validate_creator_gender(cls, v):
        if v is not None:
            allowed_values = ["ANY", "FEMALE", "GENDER_NEUTRAL", "MALE", "ORGANIZATION"]
            if v not in allowed_values:
                raise ValueError(f'Creator gender must be one of: {", ".join(allowed_values)}')
        return v

    @field_validator('platform_account_type')
    @classmethod
    def validate_platform_account_type(cls, v):
        if v is not None:
            allowed_values = ["ANY", "BUSINESS", "CREATOR", "PERSONAL", "PARTNERS", "AFFILIATES", "NULL"]
            if v not in allowed_values:
                raise ValueError(f'Platform account type must be one of: {", ".join(allowed_values)}')
        return v

    @field_validator('creator_account_type')
    @classmethod
    def validate_creator_account_type(cls, v):
        if v is not None:
            allowed_values = ["ANY", "PERSONAL", "BUSINESS", "CREATOR"]
            for item in v:
                if item not in allowed_values:
                    raise ValueError(f'Creator account type must be one of: {", ".join(allowed_values)}')
        return v

    @field_validator('audience_source')
    @classmethod
    def validate_audience_source(cls, v):
        allowed_values = ["ANY", "LIKERS", "FOLLOWERS", "COMMENTERS"]
        if v not in allowed_values:
            raise ValueError(f'Audience source must be one of: {", ".join(allowed_values)}')
        return v

    @field_validator('audience_credibility_category')
    @classmethod
    def validate_audience_credibility_category(cls, v):
        if v is not None:
            allowed_values = ["BAD", "LOW", "NORMAL", "HIGH", "EXCELLENT"]
            for item in v:
                if item not in allowed_values:
                    raise ValueError(f'Audience credibility category must be one of: {", ".join(allowed_values)}')
        return v

    @field_validator('creator_age_bracket')
    @classmethod
    def validate_creator_age_bracket(cls, v):
        if v is not None:
            allowed_values = ["OVER_18", "OVER_21"]
            if v not in allowed_values:
                raise ValueError(f'Creator age bracket must be one of: {", ".join(allowed_values)}')
        return v

    @model_validator(mode='after')
    def validate_limit_offset(self):
        if self.limit + self.offset > 500:
            raise ValueError('Limit + offset should be less than 500')
        return self

    @field_validator('work_platform_id')
    @classmethod
    def validate_work_platform_id(cls, v):
        # Map platform IDs to platform names for validation
        platform_mapping = {
            "instagram": "instagram",
            "youtube": "youtube",
            "tiktok": "tiktok",
            "twitter": "twitter",
            "twitch": "twitch"
        }

        v = v.lower().strip()
        if v not in platform_mapping and v not in SUPPORTED_PLATFORMS:
            raise ValueError(f'Work platform ID must be one of: {", ".join(platform_mapping.keys())}')
        return v

    @field_validator('description_keywords')
    @classmethod
    def validate_description_keywords(cls, v):
        if v is not None:
            v = sanitize_search_input(v)
            if len(v) > 500:
                raise ValueError('Description keywords too long (max 500 characters)')
        return v

    @model_validator(mode='after')
    def validate_follower_counts(self):
        if self.follower_count:
            min_count = self.follower_count.min
            max_count = self.follower_count.max

            if min_count is not None and max_count is not None:
                if min_count > max_count:
                    raise ValueError('follower_count min cannot be greater than max')

        return self

    @model_validator(mode='after')
    def validate_subscriber_counts(self):
        if self.subscriber_count:
            min_count = self.subscriber_count.min
            max_count = self.subscriber_count.max

            if min_count is not None and max_count is not None:
                if min_count > max_count:
                    raise ValueError('subscriber_count min cannot be greater than max')

        return self

    @model_validator(mode='after')
    def validate_content_counts(self):
        if self.content_count:
            min_count = self.content_count.min
            max_count = self.content_count.max

            if min_count is not None and max_count is not None:
                if min_count > max_count:
                    raise ValueError('content_count min cannot be greater than max')

        return self

    @model_validator(mode='after')
    def validate_age_ranges(self):
        if self.creator_age:
            min_age = self.creator_age.min
            max_age = self.creator_age.max

            if min_age is not None and max_age is not None:
                if min_age > max_age:
                    raise ValueError('creator_age min cannot be greater than max')

        if self.audience_age:
            min_age = self.audience_age.min
            max_age = self.audience_age.max

            if min_age is not None and max_age is not None:
                if min_age > max_age:
                    raise ValueError('audience_age min cannot be greater than max')

        return self

# Advanced Search Request (same as Quick Search but for different endpoint)
PhylloAdvancedSearchRequest = PhylloQuickSearchRequest

# Legacy models for backward compatibility (simplified versions)
class QuickSearchRequest(BaseModel):
    work_platform_id: str = Field(..., description="Target platform to search")
    follower_count: Optional[FollowerCountFilter] = Field(None, description="Filter by follower count")
    engagement_rate: Optional[EngagementRateFilter] = Field(None, description="Filter by engagement rate")
    is_verified: Optional[bool] = Field(None, description="Filter by verification status")
    sort_by: SortByFilter = Field(default_factory=lambda: SortByFilter(field="FOLLOWER_COUNT", order="DESCENDING"))
    limit: int = Field(10, description="Number of results", ge=1, le=100)

# Phyllo-compliant response models
class WorkPlatformAttribute(BaseModel):
    id: str
    name: str
    logo_url: Optional[str] = None

class CreatorProfileBasicDetails(BaseModel):
    platform_username: str
    url: str
    image_url: str
    follower_count: int
    subscriber_count: Optional[int] = None
    is_verified: bool
    work_platform: WorkPlatformAttribute
    full_name: str
    introduction: str
    platform_account_type: str
    gender: str
    age_group: str
    language: str
    content_count: int
    engagement_rate: float
    location: Optional[Dict] = None
    # Additional fields for advanced search response
    average_likes: Optional[int] = None
    average_views: Optional[int] = None
    creator_location: Optional[Dict] = None
    filter_match: Optional[Dict] = None
    contact_details: Optional[List[Dict]] = None

class PhylloMetadata(BaseModel):
    offset: int = 0
    limit: int = 10
    from_date: Optional[str] = None
    to_date: Optional[str] = None

class PhylloSearchResponse(BaseModel):
    data: List[CreatorProfileBasicDetails]
    metadata: PhylloMetadata

# Helper functions
def validate_platform_specific_constraints(platform: str, filters: Dict) -> List[str]:
    """Validate platform-specific constraints and return list of validation errors"""
    errors = []

    if platform not in SUPPORTED_PLATFORMS:
        return errors

    platform_config = SUPPORTED_PLATFORMS[platform]

    # Validate follower count limits
    max_followers = platform_config['max_follower_count']
    if filters.get('follower_count_min') and filters['follower_count_min'] > max_followers:
        errors.append(f'follower_count_min exceeds {platform} limit: {max_followers:,}')
    if filters.get('follower_count_max') and filters['follower_count_max'] > max_followers:
        errors.append(f'follower_count_max exceeds {platform} limit: {max_followers:,}')

    # Validate content type support
    if filters.get('content_type'):
        supported_types = platform_config['supported_content_types']
        if filters['content_type'] not in supported_types:
            errors.append(f'Content type "{filters["content_type"]}" not supported for {platform}. Supported: {", ".join(supported_types)}')

    # Validate username format if provided
    if filters.get('username'):
        username_pattern = platform_config['username_pattern']
        if not re.match(username_pattern, filters['username']):
            errors.append(f'Username format invalid for {platform}')

    return errors

def sanitize_search_input(value: str) -> str:
    """Sanitize search input to prevent injection attacks and XSS"""
    if not value:
        return value

    # Convert to string if not already
    value = str(value)

    # Remove potentially dangerous characters including SQL injection patterns
    # Remove script tags, SQL keywords, and control characters
    dangerous_patterns = [
        r'<script[^>]*>.*?</script>',  # Script tags
        r'javascript:',               # JavaScript protocol
        r'on\w+\s*=',                # Event handlers
        r'(union|select|insert|update|delete|drop|create|alter|exec|execute)\s+',  # SQL keywords
        r'[<>"\'\\\x00-\x1f\x7f-\x9f]',  # Control characters and quotes
        r'--',                        # SQL comments
        r'/\*.*?\*/',                # SQL block comments
    ]

    sanitized = value
    for pattern in dangerous_patterns:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)

    # Limit length to prevent buffer overflow attacks
    if len(sanitized) > 100:
        sanitized = sanitized[:100]

    # Remove leading/trailing whitespace
    return sanitized.strip()

def validate_rate_limits(request_count: int = 1) -> bool:
    """Basic rate limiting validation (placeholder for actual implementation)"""
    # In a real implementation, this would check against a rate limiting store
    # For now, just validate reasonable request patterns
    if request_count > 100:
        return False
    return True

def validate_content_safety(content: str) -> List[str]:
    """Validate content for safety and compliance"""
    errors = []

    if not content:
        return errors

    # Check for potentially harmful content
    harmful_patterns = [
        r'(password|secret|token|key)\s*[:=]\s*\w+',  # Credential patterns
        r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card patterns
        r'\b\d{3}-\d{2}-\d{4}\b',  # SSN patterns
        r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Email patterns (if not expected)
    ]

    for pattern in harmful_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            errors.append(f"Content contains potentially sensitive information")
            break

    # Check content length
    if len(content) > 10000:
        errors.append("Content exceeds maximum length limit")

    return errors

def validate_request_headers(headers: dict) -> List[str]:
    """Validate request headers for security"""
    errors = []

    # Check for suspicious user agents
    user_agent = headers.get('user-agent', '').lower()
    suspicious_agents = ['bot', 'crawler', 'spider', 'scraper']

    if any(agent in user_agent for agent in suspicious_agents):
        # Log but don't block - might be legitimate
        pass

    # Validate content type if present
    content_type = headers.get('content-type', '')
    if content_type and 'application/json' not in content_type.lower():
        errors.append("Invalid content type - expected application/json")

    return errors

def create_error_response(status_code: int, message: str, errors: Optional[List[str]] = None) -> HTTPException:
    """Create standardized error response"""
    detail = {
        "success": False,
        "message": message,
        "error_code": status_code
    }

    if errors:
        detail["errors"] = errors

    return HTTPException(status_code=status_code, detail=detail)

def validate_response_data(data: Any) -> Any:
    """Validate and sanitize response data before sending"""
    if isinstance(data, dict):
        sanitized = {}
        for key, value in data.items():
            # Sanitize string values
            if isinstance(value, str):
                sanitized[key] = sanitize_search_input(value)
            elif isinstance(value, (list, dict)):
                sanitized[key] = validate_response_data(value)
            else:
                sanitized[key] = value
        return sanitized
    elif isinstance(data, list):
        return [validate_response_data(item) for item in data]
    elif isinstance(data, str):
        return sanitize_search_input(data)
    else:
        return data

def log_security_event(event_type: str, details: str, request_info: Optional[dict] = None):
    """Log security events for monitoring (placeholder for actual logging)"""
    # In a real implementation, this would log to a security monitoring system
    import datetime
    timestamp = datetime.datetime.now().isoformat()

    log_entry = {
        "timestamp": timestamp,
        "event_type": event_type,
        "details": details,
        "request_info": request_info or {}
    }

    # For now, just print to console (in production, use proper logging)
    print(f"SECURITY_EVENT: {log_entry}")

def validate_search_request(filters: Dict) -> List[str]:
    """Validate search request parameters and return list of errors"""
    errors = []

    # Validate engagement rate ranges
    if filters.get('engagement_rate_min') is not None and filters.get('engagement_rate_max') is not None:
        if filters['engagement_rate_min'] > filters['engagement_rate_max']:
            errors.append('engagement_rate_min cannot be greater than engagement_rate_max')

    # Validate follower count ranges
    if filters.get('follower_count_min') is not None and filters.get('follower_count_max') is not None:
        if filters['follower_count_min'] > filters['follower_count_max']:
            errors.append('follower_count_min cannot be greater than follower_count_max')

    # Validate interests list
    if filters.get('interests'):
        if len(filters['interests']) > 20:
            errors.append('Maximum 20 interests allowed')
        for interest in filters['interests']:
            if len(interest) > 50:
                errors.append(f'Interest "{interest}" too long (max 50 characters)')

    # Platform-specific validation
    if filters.get('platform'):
        platform_errors = validate_platform_specific_constraints(filters['platform'], filters)
        errors.extend(platform_errors)

    return errors

def get_profile_analytics_data():
    """Get profile analytics data from memory database"""
    memory_db = get_memory_db()
    data_entries = list(memory_db.get("data", {}).values())

    # Filter for profile analytics data
    profile_analytics = []
    for entry in data_entries:
        if entry.get("data_type") == "profile_analytics":
            profile_analytics.append(entry["content"])

    return profile_analytics

def filter_profiles_phyllo(profiles: List[Dict], request: PhylloQuickSearchRequest) -> List[Dict]:
    """Apply Phyllo-compliant filters to profile list"""
    filtered = []

    for profile_data in profiles:
        profile = profile_data.get("profile", {})
        work_platform = profile_data.get("work_platform", {})

        # Work platform filter
        platform_name = work_platform.get("name", "").lower()
        if request.work_platform_id.lower() not in platform_name:
            continue

        # Follower count filter
        if request.follower_count:
            follower_count = profile.get("follower_count", 0)
            if request.follower_count.min and follower_count < request.follower_count.min:
                continue
            if request.follower_count.max and follower_count > request.follower_count.max:
                continue

        # Subscriber count filter
        if request.subscriber_count:
            subscriber_count = profile.get("subscriber_count", 0) or 0
            if request.subscriber_count.min and subscriber_count < request.subscriber_count.min:
                continue
            if request.subscriber_count.max and subscriber_count > request.subscriber_count.max:
                continue

        # Content count filter
        if request.content_count:
            content_count = profile.get("content_count", 0)
            if request.content_count.min and content_count < request.content_count.min:
                continue
            if request.content_count.max and content_count > request.content_count.max:
                continue

        # Verification filter
        if request.is_verified is not None:
            if profile.get("is_verified", False) != request.is_verified:
                continue

        # Creator gender filter
        if request.creator_gender and request.creator_gender != "ANY":
            profile_gender = profile.get("gender", "").upper()
            if profile_gender != request.creator_gender:
                continue

        # Creator age filter
        if request.creator_age:
            # This would need actual age data - for now skip
            pass

        # Engagement rate filter
        if request.engagement_rate:
            engagement_rate = profile.get("engagement_rate", 0.0)
            min_rate = float(request.engagement_rate.percentage_value) / 100.0
            if engagement_rate < min_rate:
                continue

        # Description keywords filter
        if request.description_keywords:
            description = profile.get("introduction", "").lower()
            keywords = request.description_keywords.lower()
            if keywords not in description:
                continue

        filtered.append(profile_data)

    return filtered

def filter_profiles(profiles: List[Dict], filters: Dict) -> List[Dict]:
    """Legacy filter function for backward compatibility"""
    filtered = []

    # Sanitize string inputs
    sanitized_filters = {}
    for key, value in filters.items():
        if isinstance(value, str):
            sanitized_filters[key] = sanitize_search_input(value)
        elif isinstance(value, list) and key == "interests":
            sanitized_filters[key] = [sanitize_search_input(item) for item in value if isinstance(item, str)]
        else:
            sanitized_filters[key] = value

    for profile_data in profiles:
        profile = profile_data.get("profile", {})
        work_platform = profile_data.get("work_platform", {})

        # Platform filter with exact matching
        if sanitized_filters.get("platform"):
            platform_name = work_platform.get("name", "").lower()
            filter_platform = sanitized_filters["platform"].lower()

            # Exact platform matching
            if filter_platform not in platform_name and platform_name not in filter_platform:
                continue

            # Validate platform-specific constraints
            platform_errors = validate_platform_specific_constraints(filter_platform, sanitized_filters)
            if platform_errors:
                # Skip profiles that don't meet platform constraints
                continue

        # Username filter with improved matching
        if sanitized_filters.get("username"):
            username = profile.get("platform_username", "").lower()
            filter_username = sanitized_filters["username"].lower()
            if filter_username not in username:
                continue

        # Follower count filters with validation
        follower_count = profile.get("follower_count", 0)
        if sanitized_filters.get("follower_count_min") is not None:
            if follower_count < sanitized_filters["follower_count_min"]:
                continue
        if sanitized_filters.get("follower_count_max") is not None:
            if follower_count > sanitized_filters["follower_count_max"]:
                continue

        # Engagement rate filters with validation
        engagement_rate = profile.get("engagement_rate", 0.0)
        if sanitized_filters.get("engagement_rate_min") is not None:
            if engagement_rate < sanitized_filters["engagement_rate_min"]:
                continue
        if sanitized_filters.get("engagement_rate_max") is not None:
            if engagement_rate > sanitized_filters["engagement_rate_max"]:
                continue

        # Verified filter
        if sanitized_filters.get("verified_only") and not profile.get("is_verified", False):
            continue

        # Gender filter with case-insensitive matching
        if sanitized_filters.get("gender"):
            profile_gender = profile.get("gender", "").upper()
            filter_gender = sanitized_filters["gender"].upper()
            if profile_gender != filter_gender:
                continue

        # Age group filter
        if sanitized_filters.get("age_group"):
            if profile.get("age_group") != sanitized_filters["age_group"]:
                continue

        # Content type filter (check if profile supports the content type)
        if sanitized_filters.get("content_type"):
            platform_name = work_platform.get("name", "").lower()
            if platform_name in SUPPORTED_PLATFORMS:
                supported_types = SUPPORTED_PLATFORMS[platform_name]['supported_content_types']
                if sanitized_filters["content_type"] not in supported_types:
                    continue

        # Location filter (enhanced with better matching)
        if sanitized_filters.get("location"):
            location_match = False
            filter_location = sanitized_filters["location"].lower()

            # Check profile location
            profile_location = profile.get("location", {})
            if isinstance(profile_location, dict):
                city = profile_location.get("city", "").lower()
                country = profile_location.get("country", "").lower()
                if filter_location in city or filter_location in country:
                    location_match = True

            # Check audience locations
            audience = profile_data.get("audience", {})
            audience_locations = audience.get("locations", [])
            for loc in audience_locations:
                if isinstance(loc, dict):
                    loc_name = loc.get("location_name", "").lower()
                    if filter_location in loc_name:
                        location_match = True
                        break

            if not location_match:
                continue

        # Interest filter (enhanced with better matching)
        if sanitized_filters.get("interests") and sanitized_filters["interests"]:
            interests_match = False

            # Get profile interests from multiple sources
            profile_interests = []

            # From top_interests
            top_interests = profile_data.get("top_interests", [])
            for interest in top_interests:
                if isinstance(interest, dict):
                    profile_interests.append(interest.get("name", "").lower())
                elif isinstance(interest, str):
                    profile_interests.append(interest.lower())

            # From audience interests
            audience = profile_data.get("audience", {})
            audience_interests = audience.get("interests", [])
            for interest in audience_interests:
                if isinstance(interest, dict):
                    profile_interests.append(interest.get("interest_name", "").lower())

            # Check if any filter interest matches profile interests
            for filter_interest in sanitized_filters["interests"]:
                filter_interest_lower = filter_interest.lower()
                for profile_interest in profile_interests:
                    if filter_interest_lower in profile_interest or profile_interest in filter_interest_lower:
                        interests_match = True
                        break
                if interests_match:
                    break

            if not interests_match:
                continue

        filtered.append(profile_data)

    return filtered

# API Endpoints

@phyllo_router.post("/profile/analytics")
async def get_profile_analytics(request: ProfileAnalyticsRequest):
    """
    Get detailed analytics for a creator profile.
    This endpoint mimics Phyllo's profile analytics API with enhanced validation and security.
    """
    try:
        # Rate limiting check
        if not validate_rate_limits():
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

        # Additional validation for profile_id
        profile_id = sanitize_search_input(request.profile_id)
        if not profile_id:
            raise HTTPException(status_code=400, detail="Invalid profile ID")

        # Content safety validation
        content_errors = validate_content_safety(profile_id)
        if content_errors:
            raise HTTPException(
                status_code=422,
                detail={
                    "message": "Content validation failed",
                    "errors": content_errors
                }
            )

        profiles = get_profile_analytics_data()

        # Find the specific profile
        target_profile = None
        for profile_data in profiles:
            if profile_data.get("id") == profile_id:
                target_profile = profile_data
                break

        if not target_profile:
            raise HTTPException(
                status_code=404,
                detail=f"Profile with ID '{profile_id}' not found"
            )

        # Validate platform exists and is supported
        work_platform = target_profile.get("work_platform", {})
        platform_name = work_platform.get("name", "").lower()
        if platform_name and platform_name not in SUPPORTED_PLATFORMS:
            raise HTTPException(
                status_code=422,
                detail=f"Profile platform '{platform_name}' is not supported"
            )

        # Build response based on include flags with safe data access
        response_data = {
            "id": target_profile.get("id"),
            "work_platform": target_profile.get("work_platform", {}),
            "profile": target_profile.get("profile", {})
        }

        if request.include_audience and "audience" in target_profile:
            # Sanitize audience data before including
            audience_data = target_profile["audience"]
            if isinstance(audience_data, dict):
                response_data["audience"] = audience_data

        if request.include_content:
            profile = target_profile.get("profile", {})
            response_data["content"] = {
                "top_contents": profile.get("top_contents", []),
                "recent_contents": profile.get("recent_contents", [])
            }

        if request.include_pricing and "pricing" in target_profile:
            # Sanitize pricing data
            pricing_data = target_profile["pricing"]
            if isinstance(pricing_data, (list, dict)):
                response_data["pricing"] = pricing_data

        # Validate response data before returning
        validated_data = validate_response_data(response_data)

        return PhylloResponse(
            success=True,
            data=validated_data,
            message="Profile analytics retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        # Log error details but don't expose internal information
        raise HTTPException(status_code=500, detail="Internal server error occurred")

@phyllo_router.post("/profile/quick-search")
async def quick_search_profiles(request: PhylloQuickSearchRequest):
    """
    Quick search for creator profiles with basic filters.
    This endpoint mimics Phyllo's quick search API with enhanced validation.
    """
    try:
        profiles = get_profile_analytics_data()

        # Apply Phyllo-compliant filters
        filtered_profiles = filter_profiles_phyllo(profiles, request)

        # Apply limit
        limited_profiles = filtered_profiles[:request.limit]

        # Build simplified response for quick search
        results = []
        for profile_data in limited_profiles:
            try:
                profile = profile_data.get("profile", {})
                work_platform = profile_data.get("work_platform", {})

                result = {
                    "id": profile_data.get("id"),
                    "platform": work_platform.get("name"),
                    "username": profile.get("platform_username"),
                    "full_name": profile.get("full_name"),
                    "follower_count": profile.get("follower_count", 0),
                    "engagement_rate": profile.get("engagement_rate", 0.0),
                    "is_verified": profile.get("is_verified", False),
                    "image_url": profile.get("image_url"),
                    "url": profile.get("url")
                }
                results.append(result)
            except Exception as profile_error:
                # Log error but continue with other profiles
                continue

        # Validate response data
        filters_applied = request.model_dump(exclude_unset=True, exclude_none=True)
        response_data = {
            "profiles": validate_response_data(results),
            "total_found": len(filtered_profiles),
            "returned": len(results),
            "limit": request.limit,
            "filters_applied": filters_applied
        }

        return PhylloResponse(
            success=True,
            data=response_data,
            message=f"Found {len(filtered_profiles)} profiles matching criteria"
        )

    except HTTPException:
        raise
    except Exception as e:
        log_security_event("API_ERROR", f"Quick search error: {str(e)}")
        raise create_error_response(500, "Internal server error occurred")

@phyllo_router.post("/profile/search")
async def advanced_search_profiles(request: PhylloAdvancedSearchRequest):
    """
    Advanced search for creator profiles with comprehensive filters.
    This endpoint mimics Phyllo's advanced search API with enhanced validation.
    """
    try:
        profiles = get_profile_analytics_data()

        # Apply Phyllo-compliant filters
        filtered_profiles = filter_profiles_phyllo(profiles, request)

        # Validate pagination parameters
        if request.offset < 0:
            raise create_error_response(422, "Offset cannot be negative")

        if request.offset >= len(filtered_profiles) and len(filtered_profiles) > 0:
            raise create_error_response(
                422,
                f"Offset {request.offset} exceeds total results {len(filtered_profiles)}"
            )

        # Enforce 500 result limit for advanced search
        if request.offset + request.limit > 500:
            raise create_error_response(
                422,
                f"Maximum 500 results allowed. Offset ({request.offset}) + Limit ({request.limit}) = {request.offset + request.limit} exceeds limit."
            )

        # Apply pagination
        start_idx = request.offset
        end_idx = start_idx + request.limit
        paginated_profiles = filtered_profiles[start_idx:end_idx]

        # Build Phyllo-compliant response with additional fields for advanced search
        results = []
        for profile_data in paginated_profiles:
            try:
                profile = profile_data.get("profile", {})
                work_platform = profile_data.get("work_platform", {})

                # Generate mock contact details if has_contact_details filter was used
                contact_details = None
                if request.has_contact_details:
                    contact_details = [
                        {"type": "EMAIL", "value": f"{profile.get('platform_username', 'user')}@example.com"},
                        {"type": "INSTAGRAM", "value": f"@{profile.get('platform_username', 'user')}"}
                    ]

                # Generate filter match score based on applied filters
                filter_match = {
                    "score": round(random.uniform(0.7, 1.0), 3),
                    "matched_criteria": []
                }

                # Add matched criteria based on applied filters
                if request.follower_count:
                    filter_match["matched_criteria"].append("follower_count")
                if request.engagement_rate:
                    filter_match["matched_criteria"].append("engagement_rate")
                if request.creator_gender:
                    filter_match["matched_criteria"].append("creator_gender")
                if request.description_keywords:
                    filter_match["matched_criteria"].append("description_keywords")

                # Create Phyllo-compliant CreatorProfileBasicDetails with additional fields
                result = CreatorProfileBasicDetails(
                    platform_username=profile.get("platform_username", ""),
                    url=profile.get("url", ""),
                    image_url=profile.get("image_url", ""),
                    follower_count=profile.get("follower_count", 0),
                    subscriber_count=profile.get("subscriber_count"),
                    is_verified=profile.get("is_verified", False),
                    work_platform=WorkPlatformAttribute(
                        id=work_platform.get("id", ""),
                        name=work_platform.get("name", ""),
                        logo_url=work_platform.get("logo_url")
                    ),
                    full_name=profile.get("full_name", ""),
                    introduction=profile.get("introduction", ""),
                    platform_account_type=profile.get("platform_account_type", "CREATOR"),
                    gender=profile.get("gender", ""),
                    age_group=profile.get("age_group", ""),
                    language=profile.get("language", "en"),
                    content_count=profile.get("content_count", 0),
                    engagement_rate=profile.get("engagement_rate", 0.0),
                    location=profile.get("location"),
                    # Additional fields for advanced search
                    average_likes=profile.get("average_likes", random.randint(100, 10000)),
                    average_views=profile.get("average_views", random.randint(1000, 100000)),
                    creator_location=profile.get("location"),  # Same as location for now
                    filter_match=filter_match,
                    contact_details=contact_details
                )

                results.append(result)
            except Exception as profile_error:
                # Log error but continue with other profiles
                continue

        # Create Phyllo-compliant metadata
        metadata = PhylloMetadata(
            offset=request.offset,
            limit=request.limit
        )

        # Return Phyllo-compliant response
        return PhylloSearchResponse(
            data=results,
            metadata=metadata
        )

    except HTTPException:
        raise
    except Exception as e:
        log_security_event("API_ERROR", f"Advanced search error: {str(e)}")
        raise create_error_response(500, "Internal server error occurred")

# Data Insertion Endpoints

class BulkProfileInsertRequest(BaseModel):
    profiles: List[Dict[str, Any]] = Field(..., description="List of profile analytics data to insert")
    batch_size: int = Field(50, description="Batch size for processing", ge=1, le=100)
    validate_only: bool = Field(False, description="Only validate data without inserting")

class SingleProfileInsertRequest(BaseModel):
    profile_data: Dict[str, Any] = Field(..., description="Profile analytics data to insert")
    overwrite: bool = Field(False, description="Overwrite existing profile if it exists")

@phyllo_router.post("/profile/insert")
async def insert_single_profile(request: SingleProfileInsertRequest):
    """
    Insert a single profile analytics record.
    This provides an API alternative to the bulk insert script.
    """
    try:
        memory_db = get_memory_db()

        profile_data = request.profile_data
        profile_id = profile_data.get("id")

        if not profile_id:
            raise HTTPException(status_code=400, detail="Profile ID is required")

        # Check if profile already exists
        existing_data = memory_db.get("data", {})
        profile_exists = False

        for data_id, data_entry in existing_data.items():
            if (data_entry.get("data_type") == "profile_analytics" and
                data_entry.get("content", {}).get("id") == profile_id):
                profile_exists = True
                if not request.overwrite:
                    raise HTTPException(
                        status_code=409,
                        detail=f"Profile {profile_id} already exists. Use overwrite=true to replace."
                    )
                # Remove existing entry for overwrite
                del existing_data[data_id]
                break

        # Validate profile data structure
        required_fields = ["id", "work_platform", "profile"]
        for field in required_fields:
            if field not in profile_data:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

        # Create new data entry
        data_id = f"data_{uuid.uuid4().hex[:8]}"
        account_id = f"account_{uuid.uuid4().hex[:8]}"

        new_entry = {
            "id": data_id,
            "account_id": account_id,
            "data_type": "profile_analytics",
            "content": profile_data,
            "created_at": datetime.now().isoformat()
        }

        # Insert into memory database
        if "data" not in memory_db:
            memory_db["data"] = {}
        memory_db["data"][data_id] = new_entry

        return PhylloResponse(
            success=True,
            data={
                "profile_id": profile_id,
                "data_id": data_id,
                "action": "updated" if profile_exists else "created",
                "message": f"Profile {'updated' if profile_exists else 'inserted'} successfully"
            },
            message="Profile data inserted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@phyllo_router.post("/profile/bulk-insert")
async def bulk_insert_profiles(request: BulkProfileInsertRequest):
    """
    Bulk insert multiple profile analytics records.
    This provides an API alternative to the bulk insert script.
    """
    try:
        memory_db = get_memory_db()

        if request.validate_only:
            # Validation mode - check data structure without inserting
            validation_results = []

            for i, profile_data in enumerate(request.profiles):
                result = {"index": i, "valid": True, "errors": []}

                # Check required fields
                required_fields = ["id", "work_platform", "profile"]
                for field in required_fields:
                    if field not in profile_data:
                        result["valid"] = False
                        result["errors"].append(f"Missing required field: {field}")

                # Check profile ID uniqueness
                profile_id = profile_data.get("id")
                if profile_id:
                    for j, other_profile in enumerate(request.profiles):
                        if j != i and other_profile.get("id") == profile_id:
                            result["valid"] = False
                            result["errors"].append(f"Duplicate profile ID: {profile_id}")
                            break

                validation_results.append(result)

            valid_count = sum(1 for r in validation_results if r["valid"])

            return PhylloResponse(
                success=True,
                data={
                    "validation_results": validation_results,
                    "summary": {
                        "total": len(request.profiles),
                        "valid": valid_count,
                        "invalid": len(request.profiles) - valid_count
                    }
                },
                message="Validation completed"
            )

        # Actual insertion mode
        results = {
            "successful": [],
            "failed": [],
            "updated": []
        }

        existing_data = memory_db.get("data", {})

        for i, profile_data in enumerate(request.profiles):
            try:
                profile_id = profile_data.get("id")

                if not profile_id:
                    results["failed"].append({
                        "index": i,
                        "profile_id": None,
                        "error": "Missing profile ID"
                    })
                    continue

                # Check if profile exists
                profile_exists = False
                existing_data_id = None

                for data_id, data_entry in existing_data.items():
                    if (data_entry.get("data_type") == "profile_analytics" and
                        data_entry.get("content", {}).get("id") == profile_id):
                        profile_exists = True
                        existing_data_id = data_id
                        break

                # Create or update entry
                if profile_exists:
                    # Update existing
                    existing_data[existing_data_id]["content"] = profile_data
                    existing_data[existing_data_id]["updated_at"] = datetime.now().isoformat()
                    results["updated"].append({
                        "index": i,
                        "profile_id": profile_id,
                        "data_id": existing_data_id
                    })
                else:
                    # Create new
                    data_id = f"data_{uuid.uuid4().hex[:8]}"
                    account_id = f"account_{uuid.uuid4().hex[:8]}"

                    new_entry = {
                        "id": data_id,
                        "account_id": account_id,
                        "data_type": "profile_analytics",
                        "content": profile_data,
                        "created_at": datetime.now().isoformat()
                    }

                    if "data" not in memory_db:
                        memory_db["data"] = {}
                    memory_db["data"][data_id] = new_entry

                    results["successful"].append({
                        "index": i,
                        "profile_id": profile_id,
                        "data_id": data_id
                    })

            except Exception as e:
                results["failed"].append({
                    "index": i,
                    "profile_id": profile_data.get("id"),
                    "error": str(e)
                })

        summary = {
            "total": len(request.profiles),
            "successful": len(results["successful"]),
            "updated": len(results["updated"]),
            "failed": len(results["failed"])
        }

        return PhylloResponse(
            success=True,
            data={
                "results": results,
                "summary": summary
            },
            message=f"Bulk insert completed: {summary['successful']} created, {summary['updated']} updated, {summary['failed']} failed"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@phyllo_router.post("/profile/bulk-insert-postgres")
async def bulk_insert_profiles_postgres(request: BulkProfileInsertRequest):
    """
    Bulk insert multiple profile analytics records into PostgreSQL.
    This uses the BulkInsertService for PostgreSQL insertion.
    """
    try:
        from ..services.bulk_insert_service import BulkInsertService
        from ..database_connection import db_manager

        if not db_manager.is_postgres_available():
            raise HTTPException(
                status_code=503,
                detail="PostgreSQL database not available. Use /profile/bulk-insert for memory database."
            )

        if request.validate_only:
            # Validation mode
            validation_results = []

            for i, profile_data in enumerate(request.profiles):
                result = {"index": i, "valid": True, "errors": []}

                # Check required fields
                required_fields = ["id", "work_platform", "profile"]
                for field in required_fields:
                    if field not in profile_data:
                        result["valid"] = False
                        result["errors"].append(f"Missing required field: {field}")

                validation_results.append(result)

            valid_count = sum(1 for r in validation_results if r["valid"])

            return PhylloResponse(
                success=True,
                data={
                    "validation_results": validation_results,
                    "summary": {
                        "total": len(request.profiles),
                        "valid": valid_count,
                        "invalid": len(request.profiles) - valid_count
                    }
                },
                message="Validation completed"
            )

        # Use the bulk insert service
        service = BulkInsertService()

        # Create a temporary JSON file with the profiles
        import tempfile
        import json

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            json.dump(request.profiles, temp_file, indent=2)
            temp_filepath = temp_file.name

        try:
            # Run bulk insert
            stats = service.bulk_insert_profiles(
                filepath=temp_filepath,
                limit=len(request.profiles)
            )

            return PhylloResponse(
                success=True,
                data={
                    "insertion_stats": stats,
                    "summary": {
                        "total": stats.get("processed", 0),
                        "successful": stats.get("successful", 0),
                        "failed": stats.get("failed", 0),
                        "duration": str(stats.get("final_counts", {}))
                    }
                },
                message=f"PostgreSQL bulk insert completed: {stats.get('successful', 0)} successful, {stats.get('failed', 0)} failed"
            )

        finally:
            # Clean up temporary file
            import os
            try:
                os.unlink(temp_filepath)
            except:
                pass

    except ImportError:
        raise HTTPException(
            status_code=503,
            detail="Bulk insert service not available. PostgreSQL dependencies may be missing."
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
