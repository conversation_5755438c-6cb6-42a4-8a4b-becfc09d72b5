#!/usr/bin/env python3
"""
Sample API calls demonstrating how to use the enhanced Phyllo API proxy.
"""

import requests
import json

BASE_URL = "http://localhost:8000/v1/social/creator"

def pretty_print(data):
    """Pretty print JSON data"""
    print(json.dumps(data, indent=2))

def test_profile_analytics():
    """Test profile analytics endpoint"""
    print("=" * 60)
    print("PROFILE ANALYTICS ENDPOINT")
    print("=" * 60)
    
    url = f"{BASE_URL}/profile/analytics"
    payload = {
        "profile_id": "cfa27c2b-6451-4433-99d7-98230dd1a1d6",
        "include_audience": True,
        "include_content": True,
        "include_pricing": True
    }
    
    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)
    
    response = requests.post(url, json=payload)
    
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def test_quick_search():
    """Test quick search endpoint"""
    print("\n" + "=" * 60)
    print("QUICK SEARCH ENDPOINT")
    print("=" * 60)
    
    url = f"{BASE_URL}/profile/quick-search"
    payload = {
        "platform": "instagram",
        "follower_count_min": 1000,
        "follower_count_max": 1000000,
        "engagement_rate_min": 0.01,
        "verified_only": False,
        "limit": 5
    }
    
    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)
    
    response = requests.post(url, json=payload)
    
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def test_advanced_search():
    """Test advanced search endpoint"""
    print("\n" + "=" * 60)
    print("ADVANCED SEARCH ENDPOINT")
    print("=" * 60)
    
    url = f"{BASE_URL}/profile/search"
    payload = {
        "platform": "youtube",
        "follower_count_min": 1000,
        "interests": ["gaming", "technology"],
        "gender": "MALE",
        "age_group": "25-34",
        "limit": 10,
        "offset": 0
    }
    
    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)
    
    response = requests.post(url, json=payload)
    
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def test_validation_errors():
    """Test validation error responses"""
    print("\n" + "=" * 60)
    print("VALIDATION ERROR EXAMPLES")
    print("=" * 60)
    
    # Test 1: Invalid platform
    print("\n1. Invalid Platform Test:")
    url = f"{BASE_URL}/profile/quick-search"
    payload = {"platform": "invalid_platform"}
    
    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)
    
    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())
    
    # Test 2: Invalid follower range
    print("\n2. Invalid Follower Range Test:")
    payload = {
        "follower_count_min": 100000,
        "follower_count_max": 1000
    }
    
    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)
    
    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())
    
    # Test 3: Invalid content type for platform
    print("\n3. Invalid Content Type for Platform Test:")
    url = f"{BASE_URL}/profile/search"
    payload = {
        "platform": "twitter",
        "content_type": "VIDEO"  # Twitter doesn't support VIDEO
    }
    
    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)
    
    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def test_security_features():
    """Test security features"""
    print("\n" + "=" * 60)
    print("SECURITY FEATURES TEST")
    print("=" * 60)
    
    # Test XSS prevention
    print("\n1. XSS Prevention Test:")
    url = f"{BASE_URL}/profile/quick-search"
    payload = {
        "username": "<script>alert('xss')</script>",
        "interests": ["<img src=x onerror=alert('xss')>"],
        "location": "javascript:alert('xss')"
    }
    
    print("Request:")
    print(f"POST {url}")
    pretty_print(payload)
    
    response = requests.post(url, json=payload)
    print(f"\nResponse Status: {response.status_code}")
    print("Response Body:")
    pretty_print(response.json())

def main():
    """Run all sample API calls"""
    print("🚀 Phyllo API Proxy - Sample API Calls")
    print("📋 This demonstrates the enhanced validation and security features")
    
    try:
        # Test basic functionality
        test_profile_analytics()
        test_quick_search()
        test_advanced_search()
        
        # Test validation
        test_validation_errors()
        
        # Test security
        test_security_features()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS COMPLETED")
        print("=" * 60)
        print("\n📖 See API_USAGE_GUIDE.md for more detailed examples")
        print("🔧 See test_enhanced_api.py for comprehensive test suite")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to the API server.")
        print("Please make sure the server is running on http://localhost:8000")
        print("Run: python -m uvicorn app.main:app --reload")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()
